# See https://help.github.com/articles/ignoring-files/ for more about ignoring files

# System
.DS_Store
*.local
*.log*

# Dependencies
node_modules/

# Local env files
.env*
!.env.example

# Build Outputs
build/
dist/
.vinxi/
.output/

# Test Outputs
coverage/
test-results/
playwright-report/
blob-report/
playwright/.cache/

# Debug
pnpm-debug.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Extra Folders
.vscode/*
!.vscode/extensions.json
.idea/
.qodo

# Added by <PERSON> Task Master
# Logs
logs
*.log
dev-debug.log
# Dependency directories
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 