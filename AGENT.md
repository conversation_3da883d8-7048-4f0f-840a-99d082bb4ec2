# AGENT.md - TanStack Router Boilerplate

## Build/Test Commands
- **Build**: `pnpm build` - Build the project
- **Dev**: `pnpm dev` - Start development server
- **Type check**: `pnpm test:types` - Run TypeScript type checking
- **Unit tests**: `pnpm test:unit` - Run all unit tests
- **Single test file**: `pnpm test:unit src/components/Button.test.tsx` - Run specific test file
- **E2E tests**: `pnpm test:e2e` - Run all Playwright tests  
- **Format**: `pnpm prettier:write` - Format code with Prettier

## Code Style & Conventions
- **Imports**: Use `@/` path aliases; group React, external libs, then internal modules
- **Components**: PascalCase files/names; prefer named exports over default exports
- **Types**: TypeScript strict mode; define interfaces for props (e.g., `PageProps`)
- **Formatting**: Prettier with 4-space tabs, 120 print width, single quotes, trailing commas
- **Linting**: Biome for linting with double quotes in JS, semicolons as needed
- **Naming**: kebab-case for files in ui/, PascalCase for components, camelCase for utilities
- **Routes**: File-based routing with `createFileRoute`; use auth boundaries (`_authenticated/`, `_public/`)
- **Styling**: Tailwind CSS with shadcn/ui components; use CVA for component variants
- **Error Handling**: Implement error boundaries; use proper error types and messages
- **State**: Zustand for global state; React Query for server state; prefer hooks pattern

## Project Structure
- Routes: `src/routes/` with auth boundaries
- Components: `src/components/` (base/, ui/, features)
- Hooks: `src/hooks/` for custom React hooks  
- Stores: `src/stores/` for Zustand state management
