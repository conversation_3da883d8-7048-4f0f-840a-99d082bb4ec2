{"name": "tanstack-router-boilerplate", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "test:types": "tsc --noEmit", "test:unit": "vitest", "test:unit:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "prettier": "prettier .", "prettier:write": "prettier . --write"}, "dependencies": {"@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@giscus/react": "^1.1.2", "@mui/icons-material": "^5.5.1", "@mui/material": "^5.5.1", "@mui/styled-engine": "^6.4.6", "@mui/styles": "^5.5.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-router": "^1.102.1", "@tanstack/react-router-devtools": "npm:@tanstack/router-devtools@^1.102.1", "@tanstack/react-router-zod-adapter": "npm:@tanstack/zod-adapter@^1.102.1", "@tanstack/react-table": "^8.20.6", "@ts-rest/core": "^3.51.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "color": "^5.0.0", "konva": "^9.3.20", "lucide-react": "^0.468.0", "raw.macro": "^0.4.2", "react": "^19.0.0", "react-color": "^2.19.3", "react-dom": "^19.0.0", "react-konva": "^19.0.2", "react-konva-utils": "^1.0.7", "react-resizable-panels": "^2.1.7", "react-use": "^17.3.2", "sonner": "^1.7.4", "spin-delay": "^2.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-deep-compare": "^1.3.0", "use-intl": "4.0.0-beta-67507cc", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@faker-js/faker": "^9.4.0", "@playwright/test": "^1.50.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@total-typescript/ts-reset": "^0.6.1", "@types/node": "^22.13.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitest/coverage-istanbul": "^3.0.5", "@vitest/ui": "^3.0.5", "autoprefixer": "^10.4.20", "babel-plugin-react-compiler": "19.0.0-beta-201e55d-20241215", "deepmerge": "^4.3.1", "jsdom": "^26.0.0", "msw": "^2.7.0", "playwright-msw": "^3.0.1", "postcss": "^8.5.2", "prettier": "^3.5.0", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "tailwindcss-plugin-animate": "npm:tailwindcss-animate@^1.0.7", "tailwindcss-plugin-aspect-ratio": "npm:@tailwindcss/aspect-ratio@^0.4.2", "tailwindcss-plugin-container-queries": "npm:@tailwindcss/container-queries@^0.1.1", "tailwindcss-plugin-forms": "npm:@tailwindcss/forms@^0.5.10", "tailwindcss-plugin-typography": "npm:@tailwindcss/typography@^0.5.16", "type-fest": "^4.34.1", "typescript": "^5.7.3", "vite": "^6.1.0", "vite-plugin-react": "npm:@vitejs/plugin-react@^4.3.4", "vite-plugin-react-swc": "npm:@vitejs/plugin-react-swc@^3.8.0", "vite-plugin-tanstack-react-router": "npm:@tanstack/router-plugin@^1.102.1", "vite-plugin-tsconfig-paths": "npm:vite-tsconfig-paths@^5.1.4", "vitest": "^3.0.5"}, "workspaces": [".", "../pikaso"]}