import type { <PERSON><PERSON><PERSON> } from "@/components/pikaso/index.all";
import { forwardRef } from "react";

interface CanvasEditorProps {
  editor: <PERSON>kas<PERSON> | null;
  canvasSize: { width: number; height: number };
  className?: string;
  isMobile?: boolean;
}

export const CanvasEditor = forwardRef<HTMLDivElement, CanvasEditorProps>(
  ({ editor, canvasSize, className = "", isMobile = false }, ref) => {
    return (
      <div
        className={`canvas-container flex-1 flex items-center justify-center bg-gray-100 overflow-hidden ${className} ${
          isMobile ? 'p-2' : 'p-4'
        }`}
      >
        <div
          ref={ref}
          className={`relative bg-white shadow-lg rounded-lg border border-gray-200 ${
            isMobile ? 'touch-manipulation' : ''
          }`}
          style={{
            width: canvasSize.width,
            height: canvasSize.height,
            maxWidth: isMobile ? 'calc(100vw - 16px)' : 'none',
            maxHeight: isMobile ? 'calc(100vh - 200px)' : 'none',
          }}
        />
      </div>
    );
  },
);

CanvasEditor.displayName = "CanvasEditor";
