import { useState, useEffect } from 'react';
import { ColorPicker } from '@/components/ColorPicker';
import { RangeSlider } from '@/components/RangeSlider';
import type { ShapeModel, LabelModel } from '@/components/pikaso/index.all';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Trash2, Copy } from 'lucide-react';

interface PropertyPanelProps {
  selectedShapes: ShapeModel[];
  onDelete: () => void;
  onDuplicate: () => void;
  isMobile?: boolean;
}

export const PropertyPanel = ({ 
  selectedShapes, 
  onDelete, 
  onDuplicate,
  isMobile = false
}: PropertyPanelProps) => {
  const [properties, setProperties] = useState<any>({});
  
  const selectedShape = selectedShapes[0];
  const isTextShape = selectedShape?.node.getClassName() === 'Group'; // Label通常是Group
  const hasSelection = selectedShapes.length > 0;

  // 实时更新属性
  useEffect(() => {
    if (!selectedShape) return;
    
    // 初始化属性
    const updateProperties = () => {
      const attrs = selectedShape.node.getAttrs();
      setProperties(attrs);
    };
    
    // 首次加载时更新属性
    updateProperties();
    
    // 监听形状变化事件
    const shape = selectedShape.node;
    shape.on('dragmove', updateProperties);
    shape.on('transform', updateProperties);
    shape.on('dragend', updateProperties);
    shape.on('transformend', updateProperties);
    
    // 清理函数
    return () => {
      shape.off('dragmove', updateProperties);
      shape.off('transform', updateProperties);
      shape.off('dragend', updateProperties);
      shape.off('transformend', updateProperties);
    };
  }, [selectedShape]);

  const handleColorChange = (color: string) => {
    if (!isTextShape || !selectedShape) return;
    
    const shape = selectedShape as LabelModel;
    shape.updateText({ fill: color });
  };

  const handleBackgroundColorChange = (color: string) => {
    if (!isTextShape || !selectedShape) return;
    
    const shape = selectedShape as LabelModel;
    shape.updateTag({ fill: color });
  };

  const handleFontSizeChange = (fontSize: number | number[]) => {
    if (!isTextShape || !selectedShape) return;
    
    const shape = selectedShape as LabelModel;
    shape.updateText({ fontSize: fontSize as number });
  };

  const handleOpacityChange = (opacity: number | number[]) => {
    if (!selectedShape) return;
    
    const opacityValue = opacity as number / 100;
    selectedShape.node.opacity(opacityValue);
    selectedShape.node.getLayer()?.draw();
    
    // 手动更新属性，确保透明度变化立即反映在UI上
    const attrs = selectedShape.node.getAttrs();
    setProperties({...attrs, opacity: opacityValue});
  };

  if (!hasSelection) {
    return (
      <div className={`${isMobile ? 'w-full' : 'w-64'} bg-white ${isMobile ? '' : 'border-l border-gray-200'} p-4`}>
        <div className="text-center text-gray-500 py-8">
          <p>选择一个元素</p>
          <p className="text-sm">来编辑其属性</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${isMobile ? 'w-full' : 'w-64'} bg-white ${isMobile ? '' : 'border-l border-gray-200'} p-4 overflow-y-auto`}>
      <div className="space-y-4">
        {/* 基本操作 */}
        <div>
          <h3 className="font-medium mb-3">操作</h3>
          <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'space-x-2'}`}>
            <Button
              size={isMobile ? "default" : "sm"}
              variant="outline"
              onClick={onDuplicate}
              className="flex-1"
            >
              <Copy size={16} className="mr-1" />
              复制
            </Button>
            <Button
              size={isMobile ? "default" : "sm"}
              variant="outline"
              onClick={onDelete}
              className="flex-1 text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
            >
              <Trash2 size={16} className="mr-1" />
              删除
            </Button>
          </div>
        </div>

        <Separator className="my-2" />

        {/* 通用属性 */}
        <div>
          <h3 className="font-medium mb-3">通用</h3>
          <RangeSlider
            title="透明度"
            defaultValue={Math.round((properties.opacity || 1) * 100)}
            SliderProps={{
              min: 0,
              max: 100,
              valueLabelDisplay: "auto",
            }}
            onChange={handleOpacityChange}
          />
        </div>

        {/* 文字专用属性 */}
        {isTextShape && (
          <>
            <Separator className="my-2" />
            <div>
              <h3 className="font-medium mb-3">文字</h3>
              <div className="space-y-3">
                <ColorPicker
                  title="文字颜色"
                  defaultColor="#000000"
                  onChange={handleColorChange}
                />
                <ColorPicker
                  title="背景颜色"
                  defaultColor="transparent"
                  onChange={handleBackgroundColorChange}
                />
                <RangeSlider
                  title="字号"
                  defaultValue={24}
                  SliderProps={{
                    min: 12,
                    max: 72,
                    valueLabelDisplay: "auto",
                  }}
                  onChange={handleFontSizeChange}
                />
              </div>
            </div>
          </>
        )}

        {/* 位置信息 */}
        <Separator className="my-2" />
        <div>
          <h3 className="font-medium mb-3">位置</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <div>X: {Math.round(properties.x || 0)}</div>
            <div>Y: {Math.round(properties.y || 0)}</div>
            {properties.width && <div>宽: {Math.round(properties.width)}</div>}
            {properties.height && <div>高: {Math.round(properties.height)}</div>}
          </div>
        </div>
      </div>
    </div>
  );
};