import type { ShapeModel } from "@/components/pikaso/index.all";
import {
  ArrowDown,
  ArrowUp,
  ChevronDown,
  ChevronUp,
  Circle,
  ImageIcon,
  Layers,
  Palette,
  Square,
  Sticker,
  Triangle,
  Type,
  MoreHorizontal,
} from "lucide-react";
import { useState } from "react";
import { Drawer } from "vaul";

interface BottomToolbarProps {
  onAddImage: () => void;
  onAddText: () => void;
  onAddShape: (type: "rect" | "circle" | "triangle") => void;
  onOpenBackgrounds: () => void;
  onOpenTemplates: () => void;
  onOpenStickers: () => void;
  onLayerChange: (action: "up" | "down" | "top" | "bottom") => void;
  isMobile?: boolean;
}

export const BottomToolbar = ({
  onAddImage,
  onAddText,
  onAddShape,
  onOpenBackgrounds,
  onOpenTemplates,
  onOpenStickers,
  onLayerChange,
  isMobile = false,
}: BottomToolbarProps) => {
  const [moreToolsOpen, setMoreToolsOpen] = useState(false);

  const primaryTools = [
    {
      icon: ImageIcon,
      label: "图片",
      onClick: onAddImage,
    },
    {
      icon: Type,
      label: "文字",
      onClick: onAddText,
    },
    {
      icon: Square,
      label: "矩形",
      onClick: () => onAddShape("rect"),
    },
    {
      icon: Circle,
      label: "圆形",
      onClick: () => onAddShape("circle"),
    },
    {
      icon: Layers,
      label: "背景",
      onClick: onOpenBackgrounds,
    },
  ];

  const secondaryTools = [
    {
      icon: Triangle,
      label: "三角形",
      onClick: () => onAddShape("triangle"),
    },
    {
      icon: Palette,
      label: "模板",
      onClick: onOpenTemplates,
    },
    {
      icon: Sticker,
      label: "贴纸",
      onClick: onOpenStickers,
    },
    {
      icon: ArrowUp,
      label: "上移",
      onClick: () => onLayerChange("up"),
    },
    {
      icon: ArrowDown,
      label: "下移",
      onClick: () => onLayerChange("down"),
    },
    {
      icon: ChevronUp,
      label: "置顶",
      onClick: () => onLayerChange("top"),
    },
    {
      icon: ChevronDown,
      label: "置底",
      onClick: () => onLayerChange("bottom"),
    },
  ];

  const allTools = [...primaryTools, ...secondaryTools];

  if (!isMobile) {
    // 桌面端显示所有工具
    return (
      <div className="bg-slate-800 text-white">
        <div className="flex justify-around items-center py-3 px-2">
          {allTools.map((item, index) => {
            const Icon = item.icon;
            return (
              <button
                key={index}
                type="button"
                className="flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-slate-700 transition-colors min-w-0"
                onClick={item.onClick}
              >
                <Icon size={24} className="flex-shrink-0" />
                <span className="text-xs whitespace-nowrap">{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>
    );
  }

  // 移动端显示主要工具 + 更多按钮
  return (
    <>
      <div className="bg-slate-800 text-white">
        <div className="flex justify-around items-center py-3 px-2">
          {primaryTools.map((item, index) => {
            const Icon = item.icon;
            return (
              <button
                key={index}
                type="button"
                className="flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-slate-700 transition-colors min-w-0 flex-1"
                onClick={item.onClick}
              >
                <Icon size={20} className="flex-shrink-0" />
                <span className="text-xs whitespace-nowrap">{item.label}</span>
              </button>
            );
          })}
          <button
            type="button"
            className="flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-slate-700 transition-colors min-w-0 flex-1"
            onClick={() => setMoreToolsOpen(true)}
          >
            <MoreHorizontal size={20} className="flex-shrink-0" />
            <span className="text-xs whitespace-nowrap">更多</span>
          </button>
        </div>
      </div>

      {/* 移动端更多工具抽屉 */}
      <Drawer.Root open={moreToolsOpen} onOpenChange={setMoreToolsOpen}>
        <Drawer.Portal>
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-40" />
          <Drawer.Content className="bg-white h-[50vh] fixed bottom-0 left-0 right-0 outline-none rounded-t-xl z-50">
            <div className="flex flex-col h-full">
              <div className="flex-shrink-0 mx-auto w-12 h-1.5 bg-gray-300 rounded-full mt-3 mb-4" />
              <div className="px-4 pb-2">
                <h3 className="text-lg font-semibold text-gray-900">更多工具</h3>
              </div>
              <div className="flex-1 overflow-y-auto px-4">
                <div className="grid grid-cols-3 gap-4 pb-4">
                  {secondaryTools.map((item, index) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={index}
                        type="button"
                        className="flex flex-col items-center space-y-2 p-4 rounded-lg hover:bg-gray-100 transition-colors"
                        onClick={() => {
                          item.onClick();
                          setMoreToolsOpen(false);
                        }}
                      >
                        <Icon size={24} className="flex-shrink-0 text-gray-700" />
                        <span className="text-sm text-gray-700">{item.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    </>
  );
};
