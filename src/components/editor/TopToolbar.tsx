import { ExportBoard } from "@/components/ExportBoard";
import { ImportBoard } from "@/components/ImportBoard";
import type { <PERSON><PERSON><PERSON> } from "@/components/pikaso/index.all";
import { Button } from "@/components/ui/button";
import { Eye, Redo, Save, Undo, Menu } from "lucide-react";

interface TopToolbarProps {
  editor: Pikaso | null;
  onSave?: () => void;
  onPreview?: () => void;
  onExport?: () => void;
  onImport?: () => void;
  isMobile?: boolean;
}

export const TopToolbar = ({
  editor,
  onSave,
  onPreview,
  onExport,
  onImport,
  isMobile = false,
}: TopToolbarProps) => {
  if (isMobile) {
    // 移动端简化版工具栏
    return (
      <div className="flex justify-between items-center px-4 py-2 bg-white border-b border-gray-200">
        {/* 左侧 - 撤销重做 */}
        <div className="flex items-center space-x-1">
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.undo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Undo size={16} />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.redo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Redo size={16} />
          </Button>
        </div>

        {/* 中间 - 保存状态 */}
        <div className="flex items-center text-green-600">
          <Save size={16} className="mr-1" />
          <span className="text-sm">已保存</span>
        </div>

        {/* 右侧 - 主要操作 */}
        <div className="flex items-center space-x-1">
          <Button size="sm" variant="outline" onClick={onPreview}>
            <Eye size={16} />
          </Button>
          <Button
            size="sm"
            variant="default"
            className="bg-green-600 hover:bg-green-700"
            onClick={onSave}
          >
            <Save size={16} />
          </Button>
        </div>
      </div>
    );
  }

  // 桌面端完整工具栏
  return (
    <div className="flex justify-between items-center px-4 py-2 bg-white border-b border-gray-200">
      {/* 左侧操作区 */}
      <div className="flex items-center space-x-2">
        {/* 保存状态 */}
        <div className="flex items-center text-green-600 mr-4">
          <Save size={18} className="mr-2" />
          <span className="text-sm">已保存</span>
        </div>

        {/* 撤销重做 */}
        <div className="flex items-center space-x-1">
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.undo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Undo size={16} className="mr-1" />
            撤销
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => editor?.redo()}
            disabled={!editor}
            className="min-w-0 px-2"
          >
            <Redo size={16} className="mr-1" />
            重做
          </Button>
        </div>

        {/* 导入导出 */}
        <div className="flex items-center space-x-1 ml-4">
          {/* 使用 ImportBoard 组件替代原来的导入按钮 */}
          <ImportBoard editor={editor} />

          {/* 使用 ExportBoard 组件替代原来的导出按钮 */}
          <ExportBoard editor={editor} />
        </div>
      </div>

      {/* 右侧操作区 */}
      <div className="flex items-center space-x-2">
        <Button size="sm" variant="default" onClick={onPreview}>
          <Eye size={16} className="mr-1" />
          预览
        </Button>
        <Button
          size="sm"
          variant="default"
          className="bg-green-600 hover:bg-green-700"
          onClick={onSave}
        >
          <Save size={16} className="mr-1" />
          保存
        </Button>
      </div>
    </div>
  );
};
