import type <PERSON><PERSON><PERSON> from "@/components/pikaso";
import {
  Captions,
  Expand,
  Maximize,
  Replace as <PERSON>lace<PERSON><PERSON>,
  Shuffle,
  Trash2,
  <PERSON><PERSON><PERSON>cle,
} from "lucide-react";
import type React from "react";

// Define SelectedImageToolbar component
export interface SelectedImageToolbarProps {
  editor: <PERSON><PERSON><PERSON> | null; // Pikaso editor instance
  onDelete: () => void;
  onCaption: () => void;
  onReplace: () => void;
  onSwap: () => void;
  onScale: () => void;
  onFillPage: () => void;
  isMobile: boolean;
}

export const SelectedImageToolbar: React.FC<SelectedImageToolbarProps> = ({
  editor,
  onDelete,
  onCaption,
  onReplace,
  onSwap,
  onScale,
  onFillPage,
  isMobile,
}) => {
  const handleClose = () => {
    if (editor?.board?.selection) {
      editor.board.selection.deselectAll();
    }
  };

  const buttonClass =
    "flex flex-col items-center text-white hover:bg-gray-700 p-1 md:p-2 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500";
  const iconSize = isMobile ? 18 : 22;
  const textSize = isMobile ? "text-[10px]" : "text-xs";

  return (
    <div className="bg-gray-800 p-1 md:p-2 flex justify-around items-center shadow-md">
      <div className="flex justify-start items-center space-x-1 md:space-x-2 overflow-x-auto">
        <button
          type="button"
          onClick={onDelete}
          className={buttonClass}
          title="删除"
        >
          <Trash2 size={iconSize} />
          <span className={`${textSize} mt-1`}>删除</span>
        </button>
        <button
          type="button"
          onClick={onCaption}
          className={buttonClass}
          title="字幕"
        >
          <Captions size={iconSize} />
          <span className={`${textSize} mt-1`}>字幕</span>
        </button>
        <button
          type="button"
          onClick={onReplace}
          className={buttonClass}
          title="替换"
        >
          <ReplaceIcon size={iconSize} />
          <span className={`${textSize} mt-1`}>替换</span>
        </button>
        <button
          type="button"
          onClick={onSwap}
          className={buttonClass}
          title="交换"
        >
          <Shuffle size={iconSize} />
          <span className={`${textSize} mt-1`}>交换</span>
        </button>
        <button
          type="button"
          onClick={onScale}
          className={buttonClass}
          title="缩放"
        >
          <Expand size={iconSize} />
          <span className={`${textSize} mt-1`}>缩放</span>
        </button>
        <button
          type="button"
          onClick={onFillPage}
          className={buttonClass}
          title="填充"
        >
          <Maximize size={iconSize} />
          <span className={`${textSize} mt-1`}>填充</span>
        </button>
      </div>
      <button
        type="button"
        onClick={handleClose}
        className="text-gray-300 hover:text-white p-1 md:p-2 rounded ml-2 md:ml-4 flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500"
        title="关闭工具栏"
      >
        <XCircle size={isMobile ? 16 : 18} />
        <span className={`${isMobile ? "hidden" : "inline"} ml-1 text-xs`}>
          关闭
        </span>
      </button>
    </div>
  );
};
