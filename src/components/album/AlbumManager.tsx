import { But<PERSON> } from "@/components/ui/button"
import { useEditorStore } from "@/stores/editorStore"
import { useNavigate } from "@tanstack/react-router"
import {
  ArrowLeft,
  Clock,
  Edit3,
  Eye,
  HelpCircle,
  Menu,
  Plus,
  RotateCcw,
  RotateCw,
  Trash2,
} from "lucide-react"
import { useEffect, useState } from "react"
import { DeleteConfirmDialog } from "./DeleteConfirmDialog"

export function AlbumManager() {
  const navigate = useNavigate()
  const { pages, addPage, deletePage, setCurrentPage } = useEditorStore()

  const [deletePageId, setDeletePageId] = useState<string | null>(null)
  const [isSaved] = useState(true)

  // 初始化示例页面（仅在开发时使用）
  useEffect(() => {
    if (pages.length === 0) {
      // 创建一些示例页面
      for (let i = 0; i < 6; i++) {
        addPage(`页面 ${i + 12}`)
      }
    }
  }, [pages.length, addPage])

  // 处理添加新页面
  const handleAddPage = () => {
    const pageId = addPage()
    setCurrentPage(pageId)
    navigate({ to: "/canva" })
  }

  // 处理删除页面
  const handleDeletePage = (pageId: string) => {
    setDeletePageId(pageId)
  }

  // 确认删除页面
  const confirmDeletePage = () => {
    if (deletePageId) {
      deletePage(deletePageId)
      setDeletePageId(null)
    }
  }

  // 处理页面编辑
  const handleEditPage = (pageId: string) => {
    setCurrentPage(pageId)
    navigate({ to: "/canva" })
  }

  // 处理预览
  const handlePreview = () => {
    // 预览功能
    console.log("Preview album")
  }

  // 处理分享
  const handleShare = () => {
    // 分享功能
    console.log("Share album")
  }

  // 处理订购
  const handleOrder = () => {
    // 订购功能
    console.log("Order album")
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 顶部导航栏 - 深色主题 */}
      <div className="bg-gray-800 text-white px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate({ to: "/" })}
              className="flex items-center space-x-2 text-white hover:bg-gray-700"
            >
              <ArrowLeft size={16} />
              <span>Exit</span>
            </Button>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="bg-purple-600 text-white border-purple-600 hover:bg-purple-700"
            >
              Share
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleOrder}
              className="bg-white text-gray-800 hover:bg-gray-100"
            >
              Order
            </Button>
          </div>
        </div>
      </div>

      {/* 状态栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-gray-600">
              <Clock size={16} />
              <span className="text-sm">{isSaved ? "Saved" : "Saving..."}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" className="p-1">
                <RotateCcw size={16} />
              </Button>
              <Button variant="ghost" size="sm" className="p-1">
                <RotateCw size={16} />
              </Button>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePreview}
            className="flex items-center space-x-2 text-purple-600"
          >
            <Eye size={16} />
            <span>Preview</span>
          </Button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {pages.length === 0 ? (
          // 空状态
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Plus size={32} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              还没有页面
            </h3>
            <p className="text-gray-500 mb-6">创建您的第一个页面开始设计</p>
            <Button onClick={handleAddPage} size="lg">
              创建第一个页面
            </Button>
          </div>
        ) : (
          // 页面网格 - 按照截图设计
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {pages.map((page, index) => (
              <div
                key={page.id}
                className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
              >
                {/* 页面内容区域 */}
                <div className="relative">
                  {/* 左侧菜单图标 */}
                  <div className="absolute top-3 left-3 z-10">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1.5 bg-white/90 backdrop-blur-sm rounded-md shadow-sm hover:bg-white"
                    >
                      <Menu size={14} className="text-gray-600" />
                    </Button>
                  </div>

                  {/* 页面预览区域 */}
                  <div
                    className="aspect-[4/3] bg-gray-50 p-3 cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => handleEditPage(page.id)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        handleEditPage(page.id)
                      }
                    }}
                    role="button"
                    tabIndex={0}
                  >
                    {/* 页面缩略图或占位符 */}
                    {page.thumbnail ? (
                      <img
                        src={page.thumbnail}
                        alt={`页面 ${index + 12} 预览`}
                        className="w-full h-full object-cover rounded-lg shadow-sm"
                      />
                    ) : (
                      /* 模拟照片拼贴布局 - 更真实的照片效果 */
                      <div className="h-full grid grid-cols-2 gap-1.5">
                        <div
                          className="bg-cover bg-center rounded-md"
                          style={{
                            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='100' height='100' fill='%23e0f2fe'/%3E%3Cpath d='M20 20h60v60H20z' fill='%23b3e5fc'/%3E%3Ccircle cx='35' cy='35' r='8' fill='%2381d4fa'/%3E%3C/svg%3E")`,
                          }}
                        />
                        <div
                          className="bg-cover bg-center rounded-md"
                          style={{
                            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='100' height='100' fill='%23e8f5e8'/%3E%3Cpath d='M15 15h70v70H15z' fill='%23c8e6c9'/%3E%3Ccircle cx='50' cy='50' r='12' fill='%23a5d6a7'/%3E%3C/svg%3E")`,
                          }}
                        />
                        <div
                          className="bg-cover bg-center rounded-md"
                          style={{
                            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='100' height='100' fill='%23fff3e0'/%3E%3Cpath d='M25 25h50v50H25z' fill='%23ffe0b2'/%3E%3Ccircle cx='40' cy='40' r='6' fill='%23ffcc02'/%3E%3C/svg%3E")`,
                          }}
                        />
                        <div
                          className="bg-cover bg-center rounded-md"
                          style={{
                            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='100' height='100' fill='%23fce4ec'/%3E%3Cpath d='M10 10h80v80H10z' fill='%23f8bbd9'/%3E%3Ccircle cx='45' cy='45' r='10' fill='%23f48fb1'/%3E%3C/svg%3E")`,
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* 页面信息和操作按钮 */}
                <div className="p-4 bg-gray-50/50">
                  <div className="text-center mb-4">
                    <span className="text-xl font-semibold text-gray-800">
                      {index + 12}
                    </span>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex justify-center space-x-6">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleAddPage()}
                      className="p-2 text-purple-600 hover:bg-purple-50 rounded-full"
                      title="添加页面"
                    >
                      <Plus size={18} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditPage(page.id)}
                      className="p-2 text-purple-600 hover:bg-purple-50 rounded-full"
                      title="编辑页面"
                    >
                      <Edit3 size={18} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeletePage(page.id)}
                      className="p-2 text-red-500 hover:bg-red-50 rounded-full"
                      title="删除页面"
                    >
                      <Trash2 size={18} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部帮助按钮 */}
      <div className="fixed bottom-6 right-6">
        <Button
          variant="outline"
          size="sm"
          className="bg-white shadow-lg border-gray-300 text-purple-600 hover:bg-purple-50"
        >
          <HelpCircle size={16} className="mr-2" />
          Help
        </Button>
      </div>

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deletePageId !== null}
        onOpenChange={(open) => !open && setDeletePageId(null)}
        onConfirm={confirmDeletePage}
        pageName={
          deletePageId
            ? pages.find((p) => p.id === deletePageId)?.name || ""
            : ""
        }
      />
    </div>
  )
}
