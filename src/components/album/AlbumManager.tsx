import { But<PERSON> } from "@/components/ui/button"
import { useEditorStore } from "@/stores/editorStore"
import {
  DragDropContext,
  Draggable,
  type DropResult,
  Droppable,
} from "@hello-pangea/dnd"
import { useNavigate } from "@tanstack/react-router"
import { ArrowLeft, Plus } from "lucide-react"
import { useState } from "react"
import { DeleteConfirmDialog } from "./DeleteConfirmDialog"
import { PageThumbnail } from "./PageThumbnail"

export function AlbumManager() {
  const navigate = useNavigate()
  const { pages, addPage, deletePage, reorderPages, setCurrentPage } =
    useEditorStore()

  const [deletePageId, setDeletePageId] = useState<string | null>(null)

  // 处理添加新页面
  const handleAddPage = () => {
    const pageId = addPage()
    // 跳转到编辑器页面
    setCurrentPage(pageId)
    navigate({ to: "/canva" })
  }

  // 处理删除页面
  const handleDeletePage = (pageId: string) => {
    setDeletePageId(pageId)
  }

  // 确认删除页面
  const confirmDeletePage = () => {
    if (deletePageId) {
      deletePage(deletePageId)
      setDeletePageId(null)
    }
  }

  // 处理页面点击
  const handlePageClick = (pageId: string) => {
    setCurrentPage(pageId)
    navigate({ to: "/canva" })
  }

  // 处理拖拽排序
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return

    const sourceIndex = result.source.index
    const destinationIndex = result.destination.index

    if (sourceIndex !== destinationIndex) {
      reorderPages(sourceIndex, destinationIndex)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate({ to: "/" })}
              className="flex items-center space-x-2"
            >
              <ArrowLeft size={16} />
              <span>返回首页</span>
            </Button>
            <h1 className="text-xl font-semibold text-gray-900">相册管理</h1>
          </div>
          <Button
            onClick={handleAddPage}
            className="flex items-center space-x-2"
          >
            <Plus size={16} />
            <span>添加新页面</span>
          </Button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {pages.length === 0 ? (
          // 空状态
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Plus size={32} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              还没有页面
            </h3>
            <p className="text-gray-500 mb-6">创建您的第一个页面开始设计</p>
            <Button onClick={handleAddPage} size="lg">
              创建第一个页面
            </Button>
          </div>
        ) : (
          // 页面网格
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="pages" direction="horizontal">
              {(provided) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
                >
                  {pages.map((page, index) => (
                    <Draggable
                      key={page.id}
                      draggableId={page.id}
                      index={index}
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className={`${
                            snapshot.isDragging ? "opacity-50" : ""
                          }`}
                        >
                          <PageThumbnail
                            page={page}
                            onClick={() => handlePageClick(page.id)}
                            onDelete={() => handleDeletePage(page.id)}
                          />
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </div>

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deletePageId !== null}
        onOpenChange={(open) => !open && setDeletePageId(null)}
        onConfirm={confirmDeletePage}
        pageName={
          deletePageId
            ? pages.find((p) => p.id === deletePageId)?.name || ""
            : ""
        }
      />
    </div>
  )
}
