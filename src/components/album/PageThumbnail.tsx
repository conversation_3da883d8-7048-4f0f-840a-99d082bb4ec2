import { useState } from "react"
import { Trash2, Edit3, MoreVertical } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { useEditorStore, type AlbumPage } from "@/stores/editorStore"

interface PageThumbnailProps {
  page: AlbumPage
  onClick: () => void
  onDelete: () => void
}

export function PageThumbnail({ page, onClick, onDelete }: PageThumbnailProps) {
  const { updatePage } = useEditorStore()
  const [isEditing, setIsEditing] = useState(false)
  const [editName, setEditName] = useState(page.name)

  // 处理名称编辑
  const handleNameEdit = () => {
    setIsEditing(true)
    setEditName(page.name)
  }

  // 保存名称
  const handleNameSave = () => {
    if (editName.trim() && editName !== page.name) {
      updatePage(page.id, { name: editName.trim() })
    }
    setIsEditing(false)
  }

  // 取消编辑
  const handleNameCancel = () => {
    setEditName(page.name)
    setIsEditing(false)
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleNameSave()
    } else if (e.key === "Escape") {
      handleNameCancel()
    }
  }

  return (
    <div className="group relative bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      {/* 缩略图区域 */}
      <div
        className="aspect-[4/3] bg-gray-100 cursor-pointer relative overflow-hidden"
        onClick={onClick}
      >
        {page.thumbnail ? (
          <img
            src={page.thumbnail}
            alt={page.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-2 bg-gray-200 rounded-lg flex items-center justify-center">
                <Edit3 size={20} />
              </div>
              <p className="text-sm">空白页面</p>
            </div>
          </div>
        )}

        {/* 悬停时显示的操作按钮 */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <Button
            variant="secondary"
            size="sm"
            className="bg-white text-gray-900 hover:bg-gray-100"
            onClick={(e) => {
              e.stopPropagation()
              onClick()
            }}
          >
            编辑
          </Button>
        </div>

        {/* 右上角菜单按钮 */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                className="w-8 h-8 p-0 bg-white hover:bg-gray-100"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical size={14} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleNameEdit}>
                <Edit3 size={14} className="mr-2" />
                重命名
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 size={14} className="mr-2" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 页面信息 */}
      <div className="p-3">
        {isEditing ? (
          <Input
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            onBlur={handleNameSave}
            onKeyDown={handleKeyDown}
            className="text-sm font-medium"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div>
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {page.name}
            </h3>
            <p className="text-xs text-gray-500 mt-1">
              {new Date(page.updatedAt).toLocaleDateString("zh-CN", {
                month: "short",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
