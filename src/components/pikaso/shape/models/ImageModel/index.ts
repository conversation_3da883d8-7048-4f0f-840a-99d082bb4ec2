import type Konva from "konva";

import type { Board } from "../../../Board";
import { isNode } from "../../../utils/detect-environment";
import { rotateAroundCenter } from "../../../utils/rotate-around-center";

import type { ShapeConfig } from "../../../types";
import { ShapeModel } from "../../ShapeModel";

export class ImageModel extends ShapeModel<Konva.Image, Konva.ImageConfig> {
  /**
   * 创建一个新的图像模型
   *
   * @param board 画板实例
   * @param node 图像节点
   * @param config 配置选项
   */
  constructor(board: Board, node: Konva.Image, config: ShapeConfig = {}) {
    super(board, node, config);

    // 绑定双击事件
    node.on("dblclick", this.startCropping.bind(this));

    // 添加移动端双击支持
    let lastTap = 0;
    node.on("touchend", (e) => {
      const currentTime = new Date().getTime();
      const tapLength = currentTime - lastTap;
      if (tapLength < 300 && tapLength > 0) {
        this.startCropping(e);
      }
      lastTap = currentTime;
    });
  }

  /**
   * @inheritdoc
   */
  public get type(): string {
    return "image";
  }

  /**
   * @inheritdoc
   * @override
   */
  public rotate(theta: number) {
    rotateAroundCenter(this.node, theta);

    this.board.events.emit("shape:rotate", {
      shapes: [this],
    });
  }

  private startCropping(e: Konva.KonvaEventObject<MouseEvent>) {
    console.log("startCropping");
    e.cancelBubble = true;
    this.board.cropper.start({
      // 裁剪配置
      circular: false,
      fixed: false,
    });
  }
}
