# Filter

滤镜是为 [Background](/api/classes/Background.html) 和 [Shapes](/api/classes/ShapeModel.html) 添加视觉效果的最佳方式。

[此页面包含所有可用滤镜的完整列表](/api/index.html#Filters)

## 基本用法

```js
// 为背景图像添加模糊效果
editor.board.background.image.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 20
  }
})

// 为所有选定的形状添加对比度滤镜
editor.selection.addFilter({
  name: 'Contrast',
  options: {
    contrast: 30
  }
})

// 为所有选定的形状添加多个滤镜
editor.selection.addFilter([
  {
    name: 'Contrast',
    options: {
      contrast: 30
    }
  }, 
  {
    name: 'Blur',
    options: {
      blurRadius: 20
    }
  }
])

// 从选定项目中移除对比度滤镜
editor.selection.removeFilter({ name: 'Contrast' })

// 移除多个滤镜
editor.selection.removeFilter([
  { name: 'Blur' },
  { name: 'Contrast' }
])

// 为形状添加蒙版滤镜
const shape = editor.shapes.circle.insert({ /* 配置 */ })

shape.addFilter({
  name: 'Mask',
  options: {
    threshold: 10
  }
})

// 从形状中移除蒙版滤镜
shape.removeFilter({ name: 'Mask' })

// 添加自定义滤镜
shape.addFilter({
  customFn: imageData => theCustomFilter(imageData)
})

// 直接将滤镜应用于形状
editor.filters.apply(<形状列表>, <滤镜列表>)

// 直接移除形状的滤镜
editor.filters.remove(<形状列表>, <滤镜列表>)
```

## 滤镜示例

以下是一个滤镜应用示例，展示如何向背景图像添加各种滤镜效果：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function FiltersExample() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });

  useEffect(() => {
    if (!editor) return;
    
    // 加载随机背景图像
    editor.board.background.setImageFromUrl(
      `https://source.unsplash.com/random/1000x400?hash=${Math.random()}`
    );
  }, [editor]);

  // 应用模糊滤镜
  const applyBlur = () => {
    if (!editor) return;
    
    editor.board.background.image.addFilter({
      name: 'Blur',
      options: {
        blurRadius: 10
      }
    });
  };

  // 应用对比度滤镜
  const applyContrast = () => {
    if (!editor) return;
    
    editor.board.background.image.addFilter({
      name: 'Contrast',
      options: {
        contrast: 30
      }
    });
  };

  // 应用灰度滤镜
  const applyGrayscale = () => {
    if (!editor) return;
    
    editor.board.background.image.addFilter({
      name: 'Grayscale'
    });
  };

  // 清除所有滤镜
  const clearFilters = () => {
    if (!editor) return;
    
    editor.board.background.image.removeFilter({
      name: '*'  // 移除所有滤镜
    });
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '400px'
        }}
      />
      
      <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'flex-end' }}>
        <button onClick={applyBlur} style={{ marginRight: '8px' }}>
          模糊
        </button>
        <button onClick={applyContrast} style={{ marginRight: '8px' }}>
          对比度
        </button>
        <button onClick={applyGrayscale} style={{ marginRight: '8px' }}>
          灰度
        </button>
        <button onClick={clearFilters} style={{ marginRight: '8px' }}>
          清除滤镜
        </button>
      </div>
    </div>
  );
}
```

## 常用滤镜

Pikaso 提供了许多内置滤镜，以下是一些常用的滤镜：

### Blur（模糊）

```js
shape.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 10  // 模糊半径，值越大越模糊
  }
})
```

### Brightness（亮度）

```js
shape.addFilter({
  name: 'Brightness',
  options: {
    brightness: 0.5  // 亮度值，范围从-1到1
  }
})
```

### Contrast（对比度）

```js
shape.addFilter({
  name: 'Contrast',
  options: {
    contrast: 30  // 对比度值，正值增加对比度，负值降低对比度
  }
})
```

### Grayscale（灰度）

```js
shape.addFilter({
  name: 'Grayscale'  // 将图像转换为灰度
})
```

### Invert（反转）

```js
shape.addFilter({
  name: 'Invert'  // 反转图像的颜色
})
```

### Noise（噪点）

```js
shape.addFilter({
  name: 'Noise',
  options: {
    noise: 0.4  // 噪点强度，范围从0到1
  }
})
```

### Pixelate（像素化）

```js
shape.addFilter({
  name: 'Pixelate',
  options: {
    pixelSize: 10  // 像素大小
  }
})
```

### Sepia（棕褐色）

```js
shape.addFilter({
  name: 'Sepia'  // 应用棕褐色滤镜
})
```

### Threshold（阈值）

```js
shape.addFilter({
  name: 'Threshold',
  options: {
    threshold: 0.5  // 阈值，范围从0到1
  }
})
```

## 组合滤镜

您可以组合多个滤镜来创建复杂的视觉效果：

```js
// 创建复古风格的照片效果
shape.addFilter([
  {
    name: 'Sepia'  // 首先应用棕褐色
  },
  {
    name: 'Vignette',  // 然后添加晕影
    options: {
      amount: 0.5,
      innerRadius: 0,
      outerRadius: 0.8
    }
  },
  {
    name: 'Noise',  // 最后添加一些噪点
    options: {
      noise: 0.1
    }
  }
]);
```

## 为选中的多个形状应用滤镜

您可以同时为多个选中的形状应用滤镜：

```js
// 选择多个形状
editor.selection.select(shape1);
editor.selection.select(shape2, true);  // true 表示多选

// 为所有选中的形状应用滤镜
editor.selection.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 5
  }
});
```

## 创建自定义滤镜

您可以创建自己的自定义滤镜。详情请参阅 [创建自定义滤镜](/advanced/create-custom-filters) 页面。

基本示例：

```js
// 创建一个自定义滤镜函数
function customRedTint(imageData) {
  const data = imageData.data;
  for (let i = 0; i < data.length; i += 4) {
    data[i] = Math.max(data[i], 100);  // 增强红色通道
  }
  return imageData;
}

// 应用自定义滤镜
shape.addFilter({
  customFn: customRedTint
});
```

这个例子创建了一个自定义滤镜，它增强图像中的红色通道，使图像具有红色色调。
