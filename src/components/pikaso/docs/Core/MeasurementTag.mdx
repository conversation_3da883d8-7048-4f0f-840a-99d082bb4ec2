# Measurement Tag

Pikaso 具有内置的测量标签辅助功能，它将在绘制或变换形状时显示形状的尺寸。

测量标签的样式可以根据您的需求进行自定义。

默认情况下，此功能是禁用的，但可以在初始配置时启用。

> 注意：此功能自 v2.5.0 版本起可用。

## 基本配置

```js
// 只能在创建画布编辑器时启用测量标签
new Pikaso({
  /* 其他配置 */
  measurement: {
    margin: 20, // 标签与形状之间的边距
    background: {
      cornerRadius: 5, // 背景圆角半径
      fill: 'purple' // 背景填充颜色
    },
    text: {
      fill: '#fff', // 文本颜色
      padding: 5, // 文本内边距
      fontSize: 14, // 文本字体大小
      fontStyle: 'bold' // 文本字体样式
    }
  }
})
```

## 测量标签示例

以下是一个完整的测量标签示例，展示如何启用和配置此功能：

```jsx
import { useEffect, useState } from 'react';
import usePikaso from './hooks/use-pikaso';

function MeasurementTagExample() {
  const [ref, editor] = usePikaso({
    // 启用并配置测量标签
    measurement: {
      margin: 20,
      background: {
        cornerRadius: 5,
        fill: 'purple'
      },
      text: {
        fill: '#fff',
        padding: 5,
        fontSize: 14,
        fontStyle: 'bold'
      }
    }
  });

  useEffect(() => {
    if (!editor) return;
    
    // 添加一个矩形并选中它
    const shape = editor.shapes.rect.insert({
      width: 200,
      height: 100,
      x: 400,
      y: 200,
      fill: 'red'
    });

    // 选中矩形，这样可以显示变换控件和测量标签
    shape.select();
    
    // 还可以添加其他形状来测试测量标签
    const addShapes = () => {
      // 添加一个圆形
      const circle = editor.shapes.circle.insert({
        radius: 80,
        x: 150,
        y: 150,
        fill: 'blue'
      });
      
      // 添加一个三角形
      const triangle = editor.shapes.triangle.insert({
        radius: 70,
        x: 600,
        y: 400,
        fill: 'green'
      });
    };
    
    // 延迟添加其他形状，这样用户可以先看到矩形的测量标签
    setTimeout(addShapes, 2000);
  }, [editor]);
  
  // 绘制新形状来测试测量标签
  const startDrawingRect = () => {
    if (!editor) return;
    
    // 先取消所有选择
    editor.selection.deselectAll();
    
    // 开始绘制矩形，这会在绘制过程中显示测量标签
    editor.shapes.rect.draw({
      fill: 'yellow',
      stroke: 'black',
      strokeWidth: 2
    });
  };
  
  // 绘制圆形
  const startDrawingCircle = () => {
    if (!editor) return;
    
    // 先取消所有选择
    editor.selection.deselectAll();
    
    // 开始绘制圆形，这会在绘制过程中显示测量标签
    editor.shapes.circle.draw({
      fill: 'orange',
      stroke: 'black',
      strokeWidth: 2
    });
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '500px'
        }}
      />
      
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={startDrawingRect} 
          style={{ marginRight: '10px' }}
        >
          绘制矩形
        </button>
        
        <button 
          onClick={startDrawingCircle}
        >
          绘制圆形
        </button>
      </div>
      
      <div style={{ marginTop: '10px' }}>
        <p>提示：尝试绘制或调整形状大小，您将看到显示尺寸的测量标签。</p>
      </div>
    </div>
  );
}
```

## 测量标签的工作原理

测量标签功能在以下情况下会显示形状的尺寸信息：

1. **绘制新形状时**：当用户正在绘制一个新形状时，测量标签会实时显示形状的宽度、高度或半径（根据形状类型）。
2. **调整形状大小时**：当用户使用变换控件调整已有形状的大小时，测量标签会显示更新后的尺寸。
3. **旋转形状时**：当形状被旋转时，测量标签会显示当前旋转角度。

测量标签的显示方式取决于形状的类型：

- **矩形**：显示宽度和高度（例如：200 x 100）
- **圆形**：显示半径或直径（例如：r: 80 或 d: 160）
- **椭圆形**：显示水平和垂直半径（例如：rx: 100, ry: 50）
- **多边形**：显示半径和边数（例如：r: 70, sides: 5）
- **线条**：显示长度（例如：length: 150）

## 自定义测量标签

您可以通过配置选项自定义测量标签的样式和行为：

```js
new Pikaso({
  measurement: {
    margin: 15, // 调整标签与形状之间的距离
    
    // 标签背景样式
    background: {
      cornerRadius: 0, // 方形背景
      fill: 'rgba(0, 0, 0, 0.7)', // 半透明黑色背景
      stroke: 'white', // 白色边框
      strokeWidth: 1, // 边框宽度
    },
    
    // 标签文本样式
    text: {
      fill: '#ffffff', // 白色文本
      padding: 8, // 增加内边距
      fontSize: 12, // 较小的字体
      fontFamily: 'Arial', // 自定义字体
      fontStyle: 'normal', // 普通字体样式
      align: 'center', // 文本对齐方式
    }
  }
})
```

## 注意事项

1. 测量标签功能仅在初始化 Pikaso 实例时可配置，不能在运行时启用或禁用。
2. 测量标签显示的是实际尺寸，不考虑缩放因素。
3. 当画板缩放时，测量标签会自动调整以保持可读性。
4. 在某些复杂操作或高密度形状的情况下，可能会出现标签重叠，这时可以通过调整 `margin` 值来改善可见性。
5. 测量标签仅在交互期间显示，当操作完成后会自动隐藏。
