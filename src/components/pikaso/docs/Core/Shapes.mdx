# Shapes

Pikaso 内置了多种形状，但您也可以扩展 [Shape Drawer](/api/classes/ShapeDrawer.html) 和 [Shape Model](/api/classes/ShapeModel.html) 来开发自定义形状。

[Background](/api/classes/Background.html)、[Image](/api/classes/Image.html) 和 [Text](/api/classes/Text.html) 也被视为 [Shape Model](/api/classes/ShapeModel.html)，继承了它的所有方法和属性。

## 可用形状

Pikaso 提供了以下内置形状：

### 基本形状

- **Circle（圆形）** - 创建圆形
- **Rect（矩形）** - 创建矩形或正方形
- **Ellipse（椭圆）** - 创建椭圆
- **Triangle（三角形）** - 创建三角形
- **Polygon（多边形）** - 创建具有指定边数的多边形
- **Line（线条）** - 创建直线或折线
- **Arrow（箭头）** - 创建箭头线条
  
### 特殊形状

- **Text（文本）** - 创建文本对象
- **Label（标签）** - 创建带有背景的文本标签
- **Image（图像）** - 插入图像
- **Svg（SVG）** - 使用 SVG 路径创建形状

## 形状操作

无论使用哪种形状，您都可以对它们执行以下通用操作：

### 插入形状

每种形状都可以通过其相应的 `insert` 方法添加到画板：

```js
// 插入圆形
const circle = editor.shapes.circle.insert({
  radius: 100,
  x: 300,
  y: 150,
  fill: 'red'
});

// 插入矩形
const rect = editor.shapes.rect.insert({
  width: 150,
  height: 150,
  x: 100,
  y: 100,
  fill: 'blue'
});

// 插入文本
const text = editor.shapes.text.insert({
  text: 'Pikaso 很棒！',
  x: 40,
  y: 100,
  fontSize: 35,
  fill: 'purple'
});
```

### 显示与隐藏

您可以控制形状的可见性：

```js
// 隐藏形状
shape.hide();

// 显示形状
shape.show();

// 切换形状的可见性
if (shape.isVisible()) {
  shape.hide();
} else {
  shape.show();
}
```

### 删除与恢复

形状可以被删除和恢复：

```js
// 删除形状（可撤销）
shape.delete();

// 恢复被删除的形状
shape.undelete();

// 永久删除形状（不可撤销）
shape.destroy();
```

### 翻转

形状可以水平或垂直翻转：

```js
// 水平翻转（X轴）
shape.flipX();

// 垂直翻转（Y轴）
shape.flipY();
```

### 选择与取消选择

您可以直接对形状应用选择操作：

```js
// 选择形状
shape.select();

// 取消选择
shape.deselect();

// 检查是否被选择
const isSelected = shape.isSelected();
```

### 旋转

形状可以进行旋转：

```js
// 旋转形状（角度值）
shape.rotate(45); // 旋转45度

// 获取当前旋转角度
const currentAngle = shape.rotation();
```

### 更新属性

您可以更新形状的各种属性：

```js
// 更新形状的多个属性
shape.update({
  x: 200,
  y: 150,
  fill: '#FF5733',
  stroke: 'black',
  strokeWidth: 2,
  opacity: 0.8
});
```

### 动画

形状支持动画效果：

```js
// 为形状属性添加动画
shape.to({
  duration: 0.5, // 动画持续时间（秒）
  x: 400,
  y: 300,
  rotation: 180,
  opacity: 0.5,
  // 动画完成后的回调
  onFinish: () => {
    console.log('动画完成');
  }
});
```

### 过滤器

您可以为形状添加滤镜效果：

```js
// 添加单个滤镜
shape.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 10
  }
});

// 添加多个滤镜
shape.addFilter([
  {
    name: 'Blur',
    options: {
      blurRadius: 5
    }
  },
  {
    name: 'Brighten',
    options: {
      brightness: 0.3
    }
  }
]);

// 移除滤镜
shape.removeFilter({
  name: 'Blur'
});

// 移除所有滤镜
shape.removeFilter({
  name: '*'
});
```

## 各种形状的示例

### Circle（圆形）

```js
// 创建简单的圆形
const simpleCircle = editor.shapes.circle.insert({
  radius: 50,
  x: 100,
  y: 100,
  fill: 'red'
});

// 创建带有渐变填充的圆形
const gradientCircle = editor.shapes.circle.insert({
  radius: 100,
  x: 300,
  y: 150,
  fillLinearGradientStartPoint: { x: -50, y: -50 },
  fillLinearGradientEndPoint: { x: 50, y: 50 },
  fillLinearGradientColorStops: [0, 'tomato', 1, 'red']
});

// 创建带有边框的圆形
const strokedCircle = editor.shapes.circle.insert({
  radius: 70,
  x: 500,
  y: 200,
  fill: 'yellow',
  stroke: 'black',
  strokeWidth: 5
});
```

### Rect（矩形）

```js
// 创建简单的矩形
const simpleRect = editor.shapes.rect.insert({
  width: 200,
  height: 100,
  x: 100,
  y: 100,
  fill: 'blue'
});

// 创建带有圆角的矩形
const roundedRect = editor.shapes.rect.insert({
  width: 150,
  height: 150,
  x: 350,
  y: 150,
  cornerRadius: 20,
  fill: 'green'
});

// 创建带有渐变的矩形
const gradientRect = editor.shapes.rect.insert({
  width: 150,
  height: 150,
  x: 600,
  y: 100,
  fillLinearGradientStartPoint: { x: -150, y: -150 },
  fillLinearGradientEndPoint: { x: 150, y: 150 },
  fillLinearGradientColorStops: [0, 'purple', 1, 'yellow']
});
```

### Ellipse（椭圆）

```js
// 创建椭圆
const ellipse = editor.shapes.ellipse.insert({
  radiusX: 120,
  radiusY: 80,
  x: 300,
  y: 150,
  fill: 'pink',
  stroke: 'red',
  strokeWidth: 2
});

// 创建带有渐变的椭圆
const gradientEllipse = editor.shapes.ellipse.insert({
  radiusX: 100,
  radiusY: 60,
  x: 500,
  y: 200,
  fillLinearGradientStartPoint: { x: -150, y: -150 },
  fillLinearGradientEndPoint: { x: 150, y: 150 },
  fillLinearGradientColorStops: [0, '#262626', 1, 'olive']
});
```

### Triangle（三角形）

```js
// 创建三角形
const triangle = editor.shapes.triangle.insert({
  radius: 100,
  x: 200,
  y: 150,
  fill: 'orange'
});

// 创建带有旋转的三角形
const rotatedTriangle = editor.shapes.triangle.insert({
  radius: 80,
  x: 400,
  y: 200,
  fill: 'yellow',
  rotation: 45
});
```

### Polygon（多边形）

```js
// 创建五边形
const pentagon = editor.shapes.polygon.insert({
  radius: 100,
  sides: 5,
  x: 300,
  y: 150,
  fill: 'purple'
});

// 创建六边形
const hexagon = editor.shapes.polygon.insert({
  radius: 80,
  sides: 6,
  x: 500,
  y: 200,
  fill: 'teal'
});

// 创建八边形
const octagon = editor.shapes.polygon.insert({
  radius: 70,
  sides: 8,
  x: 700,
  y: 250,
  fill: 'maroon'
});
```

### Line（线条）

```js
// 创建简单的直线
const simpleLine = editor.shapes.line.insert({
  points: [50, 50, 300, 50],
  stroke: 'blue',
  strokeWidth: 10
});

// 创建虚线
const dashedLine = editor.shapes.line.insert({
  points: [50, 130, 300, 130],
  stroke: 'purple',
  strokeWidth: 15,
  lineCap: 'round',
  lineJoin: 'round',
  dash: [29, 20, 0.001, 20]
});

// 创建折线
const polyline = editor.shapes.line.insert({
  points: [50, 200, 80, 230, 160, 210, 180, 250],
  stroke: 'tomato',
  strokeWidth: 10,
  lineJoin: 'round'
});
```

### Arrow（箭头）

```js
// 创建简单的箭头
const simpleArrow = editor.shapes.arrow.insert({
  points: [50, 50, 300, 50],
  stroke: 'blue',
  strokeWidth: 10
});

// 创建虚线箭头
const dashedArrow = editor.shapes.arrow.insert({
  points: [50, 130, 300, 130],
  stroke: 'purple',
  strokeWidth: 15,
  lineCap: 'round',
  lineJoin: 'round',
  dash: [29, 20, 0.001, 20]
});

// 创建折线箭头
const polylineArrow = editor.shapes.arrow.insert({
  points: [50, 200, 80, 230, 160, 210, 180, 250],
  stroke: 'tomato',
  strokeWidth: 10,
  lineJoin: 'round'
});
```

### Text（文本）

```js
// 创建简单的文本
const simpleText = editor.shapes.text.insert({
  text: 'Pikaso 很棒！',
  x: 40,
  y: 100,
  fontSize: 35,
  fill: 'purple'
});

// 创建带样式的文本
const styledText = editor.shapes.text.insert({
  text: '样式文本',
  x: 40,
  y: 200,
  fontSize: 40,
  fontFamily: 'Arial',
  fontStyle: 'bold',
  fill: 'green',
  stroke: 'black',
  strokeWidth: 1,
  align: 'center'
});
```

### SVG

您可以使用 SVG 路径创建复杂的形状：

```js
// 使用 SVG 路径创建形状
editor.shapes.svg.insert({
  data: 'M12,2.5L2,22l10-6.5L22,22L12,2.5z', // 星形的 SVG 路径
  fill: 'gold',
  x: 50,
  y: 50,
  scaleX: 1.5,
  scaleY: 1.5
});
```

## 常见属性

大多数形状支持以下属性：

- `x`, `y`: 形状位置
- `fill`: 填充颜色
- `stroke`: 边框颜色
- `strokeWidth`: 边框宽度
- `opacity`: 不透明度（0-1）
- `draggable`: 是否可拖动
- `visible`: 是否可见
- `rotation`: 旋转角度

更多特定形状的属性请参考相应的 API 文档：[Pikaso API 文档](/api/index.html)

## 渐变填充

大多数形状支持渐变填充：

```js
// 线性渐变
const linearGradient = editor.shapes.rect.insert({
  width: 200,
  height: 100,
  x: 100,
  y: 100,
  // 定义渐变起点和终点
  fillLinearGradientStartPoint: { x: 0, y: 0 },
  fillLinearGradientEndPoint: { x: 200, y: 100 },
  // 定义渐变颜色停止点
  fillLinearGradientColorStops: [0, 'red', 0.5, 'green', 1, 'blue']
});

// 径向渐变
const radialGradient = editor.shapes.circle.insert({
  radius: 100,
  x: 400,
  y: 200,
  // 定义渐变
  fillRadialGradientStartPoint: { x: 0, y: 0 },
  fillRadialGradientStartRadius: 0,
  fillRadialGradientEndPoint: { x: 0, y: 0 },
  fillRadialGradientEndRadius: 100,
  // 定义渐变颜色停止点
  fillRadialGradientColorStops: [0, 'white', 1, 'black']
});
```

## 创建自定义形状

如果内置形状不满足您的需求，您可以通过扩展 ShapeDrawer 和 ShapeModel 类创建自定义形状：

```js
// 继承 ShapeModel 创建自定义形状模型
class HeartModel extends Pikaso.ShapeModel {
  // 实现自定义方法
}

// 继承 ShapeDrawer 创建自定义形状绘制器
class HeartDrawer extends Pikaso.ShapeDrawer {
  // 实现绘制逻辑
  createKonvaNode(config) {
    return new Konva.Path({
      data: 'M50,15 a25,25 0 0,1 50,0 a25,25 0 0,1 0,40 l-50,40 l-50,-40 a25,25 0 0,1 0,-40 a25,25 0 0,1 50,0 z',
      ...config
    });
  }
}

// 注册自定义形状
editor.registerShape('heart', HeartDrawer, HeartModel);

// 使用自定义形状
editor.shapes.heart.insert({
  x: 100,
  y: 100,
  fill: 'red'
});
```

更多关于创建自定义形状的信息，请参阅[创建自定义形状](/advanced/create-custom-shapes)教程。
