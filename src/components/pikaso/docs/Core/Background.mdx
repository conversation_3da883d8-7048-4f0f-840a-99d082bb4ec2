# Background

Background（背景）是最低层且默认的图层。当 Pikaso 启动时，它会被自动创建。

Background 是一种特殊的 [Shape Model](/api/classes/ShapeModel.html)，它同时包含 [Image](/core/image) 和 [Rect](/core/rect)。

您可以直接访问这些模型并对它们进行操作。

```js
// 访问背景的图像模型
const backgroundImage = editor.board.background.image

// 访问背景的矩形模型
const backgroundOverlay = editor.board.background.overlay

// 可以访问 ShapeModel 的所有公共方法和属性
backgroundImage.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 10
  }
})
```

## 从 URL 加载背景图片

您可以从 URL 加载背景图片：

```js
// 从 URL 加载图像
editor.board.background.setImageFromUrl('<ImageUrl>')

// 或者，您可以使用
editor.loadFromUrl('<ImageUrl>')
```

### 示例

以下是从 URL 加载背景图像的示例：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function BackgroundUrlExample() {
  const [ref, editor] = usePikaso();

  // 加载随机背景图片
  const handleLoadRandomBackground = () => {
    if (!editor) return;
    
    // 使用 Unsplash 随机图像 API
    const randomUrl = `https://source.unsplash.com/random/600x400?hash=${Math.random()}`;
    editor.board.background.setImageFromUrl(randomUrl);
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#f5f5f5',
          width: '600px',
          height: '400px'
        }}
      />
      <div style={{ marginTop: '10px' }}>
        <button onClick={handleLoadRandomBackground}>
          加载随机背景
        </button>
      </div>
    </div>
  );
}
```

## 从文件加载背景图片

您可以从本地文件加载背景图片：

```js
// 加载图像文件
editor.board.background.setImageFromFile('<ImageFile>')

// 或者，您可以使用
editor.loadFromFile('<ImageFile>')
```

### 示例

以下是从文件加载背景图像的示例：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function BackgroundFileExample() {
  const [ref, editor] = usePikaso();

  // 处理图像文件选择
  const handleImageFile = (e) => {
    const file = e.target?.files?.[0];

    if (!file || !editor) {
      return;
    }

    // 从文件加载背景
    editor.loadFromFile(file);
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#f5f5f5',
          width: '600px',
          height: '400px'
        }}
      />
      <div style={{ marginTop: '10px' }}>
        <input
          type="file"
          accept="image/*"
          onChange={handleImageFile}
        />
      </div>
    </div>
  );
}
```

## 设置背景颜色

您可以更改背景的颜色：

```js
// 更改背景颜色
editor.board.background.fill('#262626')
```

### 示例

以下是更改背景颜色的示例：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function BackgroundColorExample() {
  const [ref, editor] = usePikaso();

  useEffect(() => {
    // 初始化时设置一个随机背景颜色
    if (editor) {
      editor.board.background.fill(getRandomColor());
    }
  }, [editor]);

  // 生成随机颜色
  function getRandomColor() {
    return `#${Math.floor(Math.random()*16777215).toString(16)}`;
  }

  // 更改背景颜色处理函数
  const handleChangeColor = () => {
    if (!editor) return;
    editor.board.background.fill(getRandomColor());
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#f5f5f5',
          width: '600px',
          height: '400px'
        }}
      />
      <div style={{ marginTop: '10px' }}>
        <button onClick={handleChangeColor}>
          更改背景颜色
        </button>
      </div>
    </div>
  );
}
```

## 高级用法

### 背景图像处理

您可以对背景图像应用滤镜和变换：

```js
// 对背景图像添加模糊滤镜
editor.board.background.image.addFilter({
  name: 'Blur',
  options: {
    blurRadius: 5
  }
});

// 设置图像不透明度
editor.board.background.image.opacity(0.8);

// 应用缩放
editor.board.background.image.scale({ x: 1.2, y: 1.2 });

// 应用旋转（以弧度为单位）
editor.board.background.image.rotation(Math.PI / 4); // 45度
```

### 背景叠加层

您可以使用背景的叠加层（overlay）创建颜色混合效果：

```js
// 设置半透明叠加层
editor.board.background.overlay.fill('rgba(0, 0, 0, 0.5)');

// 创建渐变背景
editor.board.background.overlay.fillLinearGradient({
  start: {
    x: 0,
    y: 0
  },
  end: {
    x: editor.board.stage.width(),
    y: editor.board.stage.height()
  },
  colorStops: [
    { offset: 0, color: 'red' },
    { offset: 0.5, color: 'green' },
    { offset: 1, color: 'blue' }
  ]
});

// 创建径向渐变
editor.board.background.overlay.fillRadialGradient({
  start: {
    x: editor.board.stage.width() / 2,
    y: editor.board.stage.height() / 2,
    radius: 0
  },
  end: {
    x: editor.board.stage.width() / 2,
    y: editor.board.stage.height() / 2,
    radius: editor.board.stage.width() / 2
  },
  colorStops: [
    { offset: 0, color: 'white' },
    { offset: 1, color: 'black' }
  ]
});
```

### 背景事件

您可以监听背景的事件：

```js
// 监听背景点击事件
editor.board.background.on('click', () => {
  console.log('背景被点击了');
});

// 监听背景图像加载完成事件
editor.board.background.image.on('loaded', () => {
  console.log('背景图像已加载完成');
});
