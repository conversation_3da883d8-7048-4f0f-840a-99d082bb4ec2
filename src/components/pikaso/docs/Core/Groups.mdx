# Groups

形状可以通过 [Groups](/api/classes/Groups.html) 组织成多个组。

该类提供了多种[API](/api/classes/Groups.html)用于处理组。

[Groups](/api/classes/Groups.html) 继承自 [ShapeModel](/api/classes/ShapeModel.html)，因此 [ShapeModel](/api/classes/ShapeModel.html) 的所有方法在 [Groups](/api/classes/Groups.html) 中都可以访问。

> 注意：此功能自 v2.4.0 版本起可用。

## 基本用法

```js
const circle = editor.shape.circle.insert({
  // 配置
})

const rect = editor.shapes.rect.insert({
  // 配置
})

// 在以下代码中，如果 'g1' 组不存在，将创建它并将圆形添加到其中
circle.group = 'g1'

// 也将矩形添加到 'g1' 组
rect.group = 'g1'

// 'group' getter 返回形状所属组的名称
console.log(rect.group) // g1

// hasGroup() 方法确定形状是否属于某个组
console.log(rect.hasGroup()) // true


// 除了将形状添加到组之外，还有另一种方法
editor.board.groups.attach([circle, rect], 'g1')

// 一个形状可以很容易地从组中移除
circle.group = null

// 它的功能相同，但当需要分离多个形状时更有用
editor.board.groups.dettach([circle], 'g1')

// 创建一个新组
editor.board.groups.create('g2', {
  // 配置
})

// 删除一个组。可以撤消此操作
editor.board.groups.delete('g1')

// 取消删除一个组
editor.board.groups.undelete('g1')

// destroy 方法删除该组及其形状。无法撤消此操作
editor.board.groups.destroy('g1')

// ungroup 方法分离组中的形状，然后删除该组。
// 如果组被缓存，此方法不起作用
editor.board.groups.ungroup('g1')

// API 提供了将选定形状分组的能力
editor.board.selection.group('g1')
```

## 组的作用

组的主要作用是将相关的形状集合在一起进行统一管理和操作。这有几个优点：

1. **统一移动和变换**：组中的所有形状可以作为一个单位进行移动、缩放或旋转
2. **结构组织**：可以逻辑地组织复杂图形的组成部分
3. **批量操作**：可以同时对多个形状应用相同的变换或样式更改
4. **选择管理**：更容易选择和操作相关形状的集合

## 组示例

以下是一个完整的组示例，展示如何创建、管理和操作组：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function GroupsExample() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });

  useEffect(() => {
    if (!editor) return;

    // 创建熊猫形状
    const panda = [
      // 左眼
      editor.shapes.circle.insert({
        fill: 'black',
        radius: 30,
        x: 350,
        y: 150
      }),
      // 右眼
      editor.shapes.circle.insert({
        fill: 'black',
        radius: 30,
        x: 450,
        y: 150
      }),
      // 脸
      editor.shapes.circle.insert({
        fill: 'white',
        radius: 70,
        stroke: '#262626',
        x: 400,
        y: 200
      }),
      // 右眼珠
      editor.shapes.circle.insert({
        fill: '#262626',
        strokeWidth: 9,
        stroke: 'orange',
        radius: 10,
        x: 430,
        y: 180
      }),
      // 左眼珠
      editor.shapes.circle.insert({
        fill: '#262626',
        strokeWidth: 10,
        stroke: 'orange',
        radius: 11,
        x: 370,
        y: 180
      })
    ];

    // 创建狐狸形状
    const fox = [
      // 左耳
      editor.shapes.triangle.insert({
        fill: 'orange',
        radius: 45,
        stroke: '#262626',
        x: 600,
        y: 150
      }),
      // 右耳
      editor.shapes.triangle.insert({
        fill: 'orange',
        radius: 45,
        stroke: '#262626',
        x: 700,
        y: 150
      }),
      // 脸
      editor.shapes.circle.insert({
        fill: 'orange',
        radius: 70,
        stroke: '#262626',
        x: 650,
        y: 200
      }),
      // 右眼
      editor.shapes.circle.insert({
        fill: '#262626',
        strokeWidth: 9,
        stroke: 'red',
        radius: 10,
        x: 680,
        y: 180
      }),
      // 左眼
      editor.shapes.circle.insert({
        fill: '#262626',
        strokeWidth: 10,
        stroke: 'red',
        radius: 11,
        x: 620,
        y: 180
      }),
      // 鼻子
      editor.shapes.triangle.insert({
        fill: 'red',
        radius: 15,
        x: 650,
        y: 220
      })
    ];

    // 旋转狐狸的耳朵
    fox[0].rotate(80);
    fox[1].rotate(-80);

    // 将形状分组
    editor.board.groups.attach(fox, 'fox');
    editor.board.groups.attach(panda, 'panda');
    
    // 返回清理函数
    return () => {
      editor.board.groups.destroy('fox');
      editor.board.groups.destroy('panda');
    };
  }, [editor]);
  
  // 组操作功能
  const movePanda = () => {
    if (!editor) return;
    
    // 获取熊猫组
    const pandaGroup = editor.board.groups.get('panda');
    if (pandaGroup) {
      // 移动整个组
      pandaGroup.x(pandaGroup.x() + 20);
    }
  };
  
  const scaleFox = () => {
    if (!editor) return;
    
    // 获取狐狸组
    const foxGroup = editor.board.groups.get('fox');
    if (foxGroup) {
      // 缩放整个组
      foxGroup.scale({
        x: foxGroup.scaleX() * 1.1,
        y: foxGroup.scaleY() * 1.1
      });
    }
  };
  
  const selectPanda = () => {
    if (!editor) return;
    
    // 获取熊猫组中的所有形状
    const pandaShapes = editor.board.groups.getShapes('panda');
    
    // 选择所有熊猫形状
    editor.board.selection.clear();
    pandaShapes.forEach(shape => {
      editor.board.selection.select(shape, true); // true 表示多选
    });
  };
  
  const ungroupFox = () => {
    if (!editor) return;
    
    // 解组狐狸
    editor.board.groups.ungroup('fox');
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '500px'
        }}
      />
      
      <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'center', gap: '10px' }}>
        <button onClick={movePanda}>移动熊猫</button>
        <button onClick={scaleFox}>缩放狐狸</button>
        <button onClick={selectPanda}>选择熊猫</button>
        <button onClick={ungroupFox}>解组狐狸</button>
      </div>
    </div>
  );
}
```

## API 参考

### 创建和管理组

```js
// 创建新组
editor.board.groups.create('groupName', {
  // 可选配置
  draggable: true,
  opacity: 1,
  // 其他 ShapeModel 支持的属性
});

// 将形状添加到组
editor.board.groups.attach([shape1, shape2], 'groupName');

// 从组中移除形状
editor.board.groups.dettach([shape1], 'groupName');

// 获取组对象
const group = editor.board.groups.get('groupName');

// 获取组中所有形状
const shapes = editor.board.groups.getShapes('groupName');

// 获取所有组名称
const groupNames = editor.board.groups.getNames();
```

### 组操作

```js
// 删除组（可撤销）
editor.board.groups.delete('groupName');

// 恢复被删除的组
editor.board.groups.undelete('groupName');

// 销毁组（不可撤销，会删除组中的所有形状）
editor.board.groups.destroy('groupName');

// 解组（分离组中的形状并删除组）
editor.board.groups.ungroup('groupName');

// 将选定形状分组
editor.board.selection.group('groupName');
```

### 形状与组的交互

```js
// 将形状添加到组
shape.group = 'groupName';

// 从组中移除形状
shape.group = null;

// 获取形状所属的组名
const groupName = shape.group;

// 检查形状是否属于某个组
const hasGroup = shape.hasGroup();

// 检查形状是否属于特定组
const isInGroup = shape.hasGroup('groupName');
```

## 高级用法

### 嵌套组

虽然 Pikaso 不直接支持组的嵌套，但您可以通过创建多个组并单独管理它们来模拟嵌套结构：

```js
// 创建"头部"组
editor.board.groups.attach([eye1, eye2, mouth], 'head');

// 创建"身体"组
editor.board.groups.attach([arm1, arm2, torso], 'body');

// 创建"角色"组（包含头部和身体）
const headGroup = editor.board.groups.get('head');
const bodyGroup = editor.board.groups.get('body');
editor.board.groups.attach([headGroup, bodyGroup], 'character');
```

### 组样式和变换

组继承了 ShapeModel 的所有功能，因此您可以对整个组应用样式和变换：

```js
// 获取组
const group = editor.board.groups.get('groupName');

// 设置组透明度
group.opacity(0.8);

// 旋转整个组
group.rotation(45); // 旋转 45 度

// 缩放整个组
group.scale({ x: 1.5, y: 1.5 });

// 移动整个组
group.x(group.x() + 100);
group.y(group.y() + 50);
```

### 组事件

您可以监听组事件，就像监听单个形状一样：

```js
// 获取组
const group = editor.board.groups.get('groupName');

// 监听组点击事件
group.on('click', () => {
  console.log('组被点击了');
});

// 监听组拖动事件
group.on('dragmove', () => {
  console.log('组被拖动了');
});
