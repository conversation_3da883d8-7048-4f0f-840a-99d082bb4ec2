# Snap To Grid

Snap To Grid（网格对齐）功能能够确保对象可以轻松地与图层上的任何现有对象对齐。
默认情况下，此功能是禁用的，但可以很容易地启用。

## 基本配置

```js
// Snap To Grid 线条具有以下默认样式
// {
//   stroke: '#000',
//   strokeWidth: 1,
//   dash: [2, 6],
// }
const editor = new Pikaso({
  container: document.getElementById('<YOUR_DIV_ID>'),
  snapToGrid: {}
})

// 可以覆盖线条样式
const editor = new Pikaso({
  container: document.getElementById('<YOUR_DIV_ID>'),
  snapToGrid: {
    strokeWidth: 1,
    stroke: 'purple',
    dash: [5, 5]
  }
})

// 禁用网格对齐
editor.snapGrid.disable()

// 在对象实例化后启用网格对齐
editor.snapGrid.enable()

// 设置选项
editor.snapGrid.setOptions({
  strokeWidth: 2,
  stroke: 'red'
})

// 设置网格对齐精度偏移量
editor.snapGrid.setOffset(10) // 默认值为 5
```

## 网格对齐示例

以下是一个完整的网格对齐示例，展示如何启用和配置此功能：

```jsx
import { useEffect, useState } from 'react';
import usePikaso from './hooks/use-pikaso';

function SnapGridExample() {
  const [ref, editor] = usePikaso({
    // 初始化时启用网格对齐功能
    snapToGrid: {
      strokeWidth: 1,
      stroke: 'purple',
      dash: [5, 5]
    },
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });
  
  // 跟踪网格对齐状态
  const [snapEnabled, setSnapEnabled] = useState(true);
  // 跟踪网格对齐偏移量
  const [offset, setOffset] = useState(5);

  useEffect(() => {
    if (!editor) return;
    
    // 在画板上添加一些形状
    
    // 添加三角形
    editor.shapes.triangle.insert({
      fill: 'red',
      radius: 50,
      x: 200,
      y: 100
    });

    // 添加两个圆形并将它们分组
    const shapes = [
      editor.shapes.circle.insert({
        fill: 'black',
        radius: 30,
        x: 500,
        y: 100
      }),
      editor.shapes.circle.insert({
        fill: 'blue',
        radius: 70,
        x: 600,
        y: 200
      })
    ];
    
    // 添加矩形
    editor.shapes.rect.insert({
      fill: 'orange',
      width: 200,
      height: 120,
      x: 750,
      y: 300
    });

    // 将两个圆形分组
    editor.board.groups.attach(shapes, 'g1');
  }, [editor]);
  
  // 切换网格对齐功能
  const toggleSnapToGrid = () => {
    if (!editor) return;
    
    if (snapEnabled) {
      editor.snapGrid.disable();
      setSnapEnabled(false);
    } else {
      editor.snapGrid.enable();
      setSnapEnabled(true);
    }
  };
  
  // 更改网格对齐线条样式
  const changeGridStyle = (colorChoice) => {
    if (!editor) return;
    
    let style = {};
    
    switch(colorChoice) {
      case 'red':
        style = { stroke: 'red', strokeWidth: 1, dash: [2, 2] };
        break;
      case 'blue':
        style = { stroke: 'blue', strokeWidth: 2, dash: [5, 3] };
        break;
      case 'green':
        style = { stroke: 'green', strokeWidth: 1, dash: [10, 5] };
        break;
      default:
        style = { stroke: 'purple', strokeWidth: 1, dash: [5, 5] };
    }
    
    editor.snapGrid.setOptions(style);
  };
  
  // 更改偏移量
  const changeOffset = (newOffset) => {
    if (!editor) return;
    
    editor.snapGrid.setOffset(newOffset);
    setOffset(newOffset);
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '500px'
        }}
      />
      
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={toggleSnapToGrid}
          style={{ 
            backgroundColor: snapEnabled ? 'green' : 'grey', 
            color: 'white',
            marginRight: '10px'
          }}
        >
          {snapEnabled ? '禁用网格对齐' : '启用网格对齐'}
        </button>
        
        <span style={{ marginRight: '10px' }}>网格样式：</span>
        <button onClick={() => changeGridStyle('red')} style={{ marginRight: '5px' }}>红色</button>
        <button onClick={() => changeGridStyle('blue')} style={{ marginRight: '5px' }}>蓝色</button>
        <button onClick={() => changeGridStyle('green')} style={{ marginRight: '10px' }}>绿色</button>
        
        <span style={{ marginRight: '10px' }}>偏移量 ({offset})：</span>
        <button onClick={() => changeOffset(3)} style={{ marginRight: '5px' }}>3</button>
        <button onClick={() => changeOffset(5)} style={{ marginRight: '5px' }}>5</button>
        <button onClick={() => changeOffset(10)}>10</button>
      </div>
    </div>
  );
}
```

## 网格对齐原理

Snap To Grid 功能的工作原理是在用户拖动或调整形状时，分析形状的边缘和中心点，并将其与画板上其他形状的相应位置对齐。当形状边缘或中心点接近其他形状的边缘或中心点时，会出现对齐线，并且形状会自动"吸附"到对齐位置。

这种对齐行为在以下情况下特别有用：

1. 排列多个形状时，使它们整齐对齐
2. 确保形状之间的间距一致
3. 创建精确的布局和设计

## 偏移量（Offset）

偏移量决定了形状多接近另一个形状的边缘或中心点时会触发对齐行为。较小的偏移量需要更精确的定位，而较大的偏移量允许从更远的距离进行对齐。

```js
// 设置较小的偏移量，需要更精确的定位
editor.snapGrid.setOffset(3);

// 设置默认偏移量
editor.snapGrid.setOffset(5);

// 设置较大的偏移量，允许从更远的距离进行对齐
editor.snapGrid.setOffset(10);
```

## 可视化风格

您可以自定义对齐线的外观，以适应您的设计需求：

```js
// 使用红色实线
editor.snapGrid.setOptions({
  stroke: 'red',
  strokeWidth: 1,
  dash: []
});

// 使用蓝色虚线
editor.snapGrid.setOptions({
  stroke: 'blue',
  strokeWidth: 2,
  dash: [5, 3]
});

// 使用绿色点线
editor.snapGrid.setOptions({
  stroke: 'green',
  strokeWidth: 1,
  dash: [1, 3]
});
```

## 进阶用法

### 结合分组使用

网格对齐功能在处理分组形状时也能正常工作。当移动一个组时，组中的所有形状都会遵循相同的对齐规则：

```js
// 创建一个包含多个形状的组
const shapes = [
  editor.shapes.circle.insert({ /* 配置 */ }),
  editor.shapes.rect.insert({ /* 配置 */ })
];

editor.board.groups.attach(shapes, 'myGroup');

// 启用网格对齐
editor.snapGrid.enable();

// 现在，当移动组时，组中的形状会与其他形状对齐
```

### 动态控制

您可以根据用户的操作或特定条件动态启用或禁用网格对齐功能：

```js
// 例如，在按住 Shift 键时禁用网格对齐
window.addEventListener('keydown', (event) => {
  if (event.key === 'Shift') {
    editor.snapGrid.disable();
  }
});

window.addEventListener('keyup', (event) => {
  if (event.key === 'Shift') {
    editor.snapGrid.enable();
  }
});
```

## 注意事项

1. 网格对齐功能在形状数量较多时可能会降低性能，因为系统需要计算更多的对齐可能性。
2. 对于精确布局，建议使用较小的偏移量。
3. 对于快速原型设计，较大的偏移量可能更为实用。
4. 你可以随时通过 `enable()` 和 `disable()` 方法切换网格对齐功能的状态。
