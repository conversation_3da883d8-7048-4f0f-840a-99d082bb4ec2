# Events

订阅事件是跟踪变化和操作的最佳方式。

通过 [on](/api/classes/Pikaso.html#on) 和 [off](/api/classes/Pikaso.html#off) 方法，可以轻松实现这一点。

[此页面包含所有监听器的完整列表](/api/index.html#EventListenerNames)

## 基本用法

```js
// 订阅所有事件
editor.on('*', data => {
  console.log(data)
})

// 订阅 shape:create 事件
editor.on('shape:create', data => {
  console.log(data)
})

// 取消订阅 shape:create 事件
editor.off('shape:create', data => {
  console.log(data)
})

// 订阅 shape:create 和 history:undo 事件
editor.on(['shape:create', 'history:undo'], data => {
  console.log(data)
})

// 取消订阅 shape:create 和 history:undo 事件
editor.off(['shape:create', 'history:undo'], data => {
  console.log(data)
})
```

## 事件系统示例

以下是一个实际的事件监听示例，展示如何监听和处理各种事件：

```jsx
import { useEffect, useState } from 'react';
import usePikaso from './hooks/use-pikaso';

function EventsExample() {
  // 用于存储和显示事件的状态
  const [events, setEvents] = useState([]);

  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });

  useEffect(() => {
    if (!editor) return;
    
    // 添加一些初始形状
    editor.shapes.image.insert('/tiger.svg', {
      x: 100,
      y: 100
    });

    editor.shapes.rect.insert({
      fill: 'red',
      width: 250,
      height: 100,
      x: 500,
      y: 100
    });

    // 监听所有事件
    const handleEvents = (e) => {
      // 将新事件添加到事件列表的顶部
      setEvents(state => [
        e.name + (e.data ? `: ${JSON.stringify(e.data)}` : ''),
        ...state
      ].slice(0, 50)); // 只保留最近的50个事件
    };
    
    // 订阅所有事件
    editor.on('*', handleEvents);
    
    // 清理函数
    return () => {
      editor.off('*', handleEvents);
    };
  }, [editor]);

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '500px'
        }}
      />
      
      <div style={{ marginTop: '20px' }}>
        <h3>事件日志</h3>
        <textarea
          value={events.join('\n')}
          readOnly
          style={{
            width: '100%',
            height: '300px',
            fontFamily: 'monospace'
          }}
        />
      </div>
    </div>
  );
}
```

## 常用事件

Pikaso 提供了丰富的事件系统，以下是一些常用事件：

### 形状相关事件
- `shape:create` - 创建形状时触发
- `shape:transform` - 形状变换时触发
- `shape:remove` - 删除形状时触发
- `shape:select` - 选择形状时触发
- `shape:deselect` - 取消选择形状时触发
- `shape:move` - 移动形状时触发
- `shape:rotate` - 旋转形状时触发
- `shape:scale` - 缩放形状时触发

### 历史记录事件
- `history:undo` - 撤销操作时触发
- `history:redo` - 重做操作时触发
- `history:add` - 添加历史记录时触发
- `history:reset` - 重置历史记录时触发

### 背景事件
- `background:change` - 背景变更时触发
- `background:image:load` - 背景图像加载时触发

### 选择事件
- `selection:change` - 选择变更时触发
- `selection:transform:start` - 变换开始时触发
- `selection:transform:end` - 变换结束时触发

### 绘制事件
- `drawing:start` - 开始绘制时触发
- `drawing:end` - 结束绘制时触发

## 监听特定组件的事件

除了在 Pikaso 实例上监听全局事件外，您还可以监听特定组件的事件：

```js
// 监听特定形状的事件
const circle = editor.shapes.circle.insert({
  x: 100,
  y: 100,
  radius: 50,
  fill: 'blue'
});

// 监听圆形的点击事件
circle.on('click', () => {
  console.log('圆形被点击了');
});

// 监听圆形的拖动事件
circle.on('dragmove', () => {
  console.log('圆形被拖动了');
});
```

## 在组件中使用事件监听

在 React 组件中使用事件监听时，确保在组件卸载时取消订阅事件，以防止内存泄漏：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function EventComponent() {
  const [ref, editor] = usePikaso();
  
  useEffect(() => {
    if (!editor) return;
    
    // 事件处理函数
    const handleShapeCreate = (data) => {
      console.log('新形状创建:', data);
    };
    
    // 订阅事件
    editor.on('shape:create', handleShapeCreate);
    
    // 清理函数
    return () => {
      // 取消订阅事件
      editor.off('shape:create', handleShapeCreate);
    };
  }, [editor]);
  
  return (
    <div
      ref={ref}
      style={{
        width: '100%',
        height: '400px',
        background: '#f0f0f0'
      }}
    />
  );
}
```

## 使用事件数据

事件回调函数会收到一个包含事件详细信息的数据对象，您可以利用这些数据来实现各种功能：

```js
// 根据形状创建事件更新计数器
let shapeCount = 0;

editor.on('shape:create', (data) => {
  shapeCount++;
  console.log(`已创建 ${shapeCount} 个形状`);
  console.log('新形状类型:', data.target.className);
});

// 根据形状选择事件更新 UI
editor.on('shape:select', (data) => {
  // 显示所选形状的属性
  const shape = data.target;
  console.log('所选形状属性:', {
    类型: shape.className,
    位置: { x: shape.x(), y: shape.y() },
    尺寸: shape.className === 'Circle' ? 
      { radius: shape.radius() } : 
      { width: shape.width(), height: shape.height() }
  });
});
