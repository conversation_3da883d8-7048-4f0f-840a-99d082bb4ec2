# History

Pikaso 是一个完全有状态的库，它跟踪不同状态之间的变化，允许在这些状态之间导航。

为此，提供了 [undo](/api/classes/History.html#undo)、[redo](/api/classes/History.html#redo) 和 [jump](/api/classes/History.html#jump) 功能。

## 基本用法

```js
// 回到上一个状态
editor.undo() // 或 editor.history.undo()

// 前进到下一个状态
editor.redo() // 或 editor.history.redo()

// 重新初始化画板
editor.reset()

// 跳转到特定状态
editor.history.jump(<Number>)
```

## 历史状态示例

以下是一个完整的历史记录管理示例，展示如何使用撤销、重做和重置功能：

```jsx
import { useEffect } from 'react';
import usePikaso from './hooks/use-pikaso';

function HistoryExample() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });

  useEffect(() => {
    if (!editor) return;
    
    // 初始化时创建一些随机圆形
    for (let i = 0; i < 20; i++) {
      addRandomCircle();
    }
  }, [editor]);

  // 添加随机圆形
  const addRandomCircle = () => {
    if (!editor) return;
    
    // 生成随机数值
    const radius = Math.floor(Math.random() * 40) + 40; // 40-80
    const randomColor = `#${Math.floor(Math.random() * 16777215).toString(16)}`;
    const x = Math.floor(Math.random() * (editor.board.stage.width() - 50));
    const y = Math.floor(Math.random() * (editor.board.stage.height() - 50));
    
    // 插入随机圆形
    editor.shapes.circle.insert({
      radius: radius,
      fill: randomColor,
      x: x,
      y: y
    });
  };

  // 撤销上一个动作
  const handleUndo = () => {
    if (!editor) return;
    editor.undo();
  };

  // 重做上一个被撤销的动作
  const handleRedo = () => {
    if (!editor) return;
    editor.redo();
  };

  // 重置画板
  const handleReset = () => {
    if (!editor) return;
    editor.reset();
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          background: '#CBC3E3',
          width: '100%',
          height: '300px'
        }}
      />
      
      <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <button onClick={handleUndo}>撤销</button>
          <button onClick={handleReset}>重置</button>
          <button onClick={handleRedo}>重做</button>
        </div>
        
        <button onClick={addRandomCircle}>创建圆形</button>
      </div>
    </div>
  );
}
```

## 历史记录原理

Pikaso 的历史记录系统工作原理如下：

1. 每个用户操作（如添加、移动、删除形状等）都会创建一个新的状态快照
2. 这些快照被存储在历史记录堆栈中
3. `undo()` 方法将当前状态回退到上一个状态
4. `redo()` 方法将当前状态前进到下一个状态
5. `reset()` 方法将画板重置到初始状态
6. `jump(n)` 方法允许直接跳转到历史记录中的特定状态

## API 参考

### undo()

撤销上一个操作，回到历史记录中的上一个状态。

```js
// 使用编辑器实例的方法
editor.undo();

// 或使用历史记录模块
editor.history.undo();
```

### redo()

重做之前撤销的操作，前进到历史记录中的下一个状态。

```js
// 使用编辑器实例的方法
editor.redo();

// 或使用历史记录模块
editor.history.redo();
```

### reset()

重置画板到初始状态，清除所有形状和变化。

```js
editor.reset();
```

### jump(index)

跳转到历史记录中的特定状态。

```js
// 跳转到第5个状态
editor.history.jump(5);

// 跳转到第一个状态
editor.history.jump(0);
```

### getCurrentIndex()

获取当前状态在历史记录中的索引。

```js
const currentIndex = editor.history.getCurrentIndex();
console.log(`当前处于第 ${currentIndex} 个状态`);
```

### getStatesCount()

获取历史记录中的状态总数。

```js
const statesCount = editor.history.getStatesCount();
console.log(`总共有 ${statesCount} 个状态`);
```

## 高级用法

### 历史记录监听

您可以监听历史记录的变化事件：

```js
// 监听撤销操作
editor.on('history:undo', (data) => {
  console.log('操作已撤销', data);
});

// 监听重做操作
editor.on('history:redo', (data) => {
  console.log('操作已重做', data);
});

// 监听历史记录添加
editor.on('history:add', (data) => {
  console.log('新状态已添加到历史记录', data);
});

// 监听历史记录重置
editor.on('history:reset', () => {
  console.log('历史记录已重置');
});
```

### 历史记录限制

默认情况下，Pikaso 会保存所有操作的历史记录。如果您的应用处理大量操作，可能需要限制历史记录的大小：

```js
// 创建 Pikaso 实例时设置历史记录限制
const editor = new Pikaso({
  history: {
    maxSteps: 50  // 只保存最近的50个操作
  }
});
```

### 禁用历史记录

在某些情况下，您可能希望临时禁用历史记录的创建：

```js
// 禁用历史记录
editor.history.disable();

// 执行不需要记录的操作
performBatchOperations();

// 重新启用历史记录
editor.history.enable();
```

### 批量操作

对于复杂的批量操作，您可能希望将多个变更作为单个历史记录条目：

```js
// 开始批量操作
editor.history.beginBatch();

// 执行多个操作
editor.shapes.circle.insert({ /* 配置 */ });
editor.shapes.rect.insert({ /* 配置 */ });
editor.shapes.triangle.insert({ /* 配置 */ });

// 结束批量操作，这些操作将作为一个历史记录条目
editor.history.endBatch();
```

这样，当用户点击撤销时，所有这些操作将一起被撤销，而不是一步一步地撤销每个操作。
