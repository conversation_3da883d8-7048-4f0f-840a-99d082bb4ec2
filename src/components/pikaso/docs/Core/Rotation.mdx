# Rotation

Pikaso 提供 [Transform](/api/classes/Rotation.html#transform) 和 [Straighten](/api/classes/Rotation.html#straighten) 方法来旋转 [Board](/api/classes/Board.html)、[Background](/api/classes/Background.html) 和 [Shapes](/api/classes/ShapeModel.html)。

[Transform](/api/classes/Rotation.html#transform) 和 [Straighten](/api/classes/Rotation.html#straighten) 这两种方法有根本上的区别：

- [Transform](/api/classes/Rotation.html#transform) 围绕中心点旋转画板及其形状，同时进行缩放和变换
- [Straighten](/api/classes/Rotation.html#straighten) 围绕中心点旋转画板及其形状，但不进行变换

## 基本用法

```js
// 旋转并变换画板
editor.rotation.transform(30)

// 旋转而不变换
editor.rotation.straighten(-30)

// 旋转单个形状
const shape = editor.shapes.triangle.insert({ /* 配置 */ })
shape.rotate(50)
```

## 旋转示例

以下是一个完整的旋转示例，展示如何使用不同的旋转方法：

```jsx
import { useEffect, useState } from 'react';
import usePikaso from './hooks/use-pikaso';

function RotationExample() {
  const [ref, editor] = usePikaso({});
  const [transformAngle, setTransformAngle] = useState(0);
  const [straightenAngle, setStraightenAngle] = useState(0);

  useEffect(() => {
    const initialize = async () => {
      if (!editor) return;
      
      // 加载背景图像
      await editor.loadFromUrl(
        'https://images.unsplash.com/photo-1504194104404-433180773017?auto=format&fit=crop&w=1740&q=80'
      );
      
      // 添加一些形状
      editor.shapes.image.insert('/tiger.svg', {
        x: 1000,
        y: 200,
        cropWidth: 100
      });
      
      editor.shapes.polygon.insert({
        sides: 5,
        fillLinearGradientStartPoint: { x: -50, y: -50 },
        fillLinearGradientEndPoint: { x: 50, y: 50 },
        fillLinearGradientColorStops: [0, 'blue', 1, 'yellow'],
        radius: 200,
        x: 300,
        y: 400
      });
    };
    
    initialize();
  }, [editor]);
  
  // 使用 transform 方法旋转
  const handleTransformChange = (angle) => {
    setTransformAngle(angle);
    if (editor) {
      editor.rotation.transform(angle);
    }
  };
  
  // 使用 straighten 方法旋转
  const handleStraightenChange = (angle) => {
    setStraightenAngle(angle);
    if (editor) {
      editor.rotation.straighten(angle);
    }
  };
  
  // 重置旋转
  const resetRotation = () => {
    setTransformAngle(0);
    setStraightenAngle(0);
    if (editor) {
      editor.rotation.transform(0);
      editor.rotation.straighten(0);
    }
  };

  return (
    <div>
      <div
        ref={ref}
        style={{
          margin: '0 auto',
          width: '600px',
          height: '400px',
          border: '2px solid #262626',
          background: 'skyblue'
        }}
      />
      
      <div style={{ marginTop: '20px' }}>
        <div>
          <label>
            旋转并变换 (Transform): {transformAngle}°
            <input
              type="range"
              min="-360"
              max="360"
              step="1"
              value={transformAngle}
              onChange={(e) => handleTransformChange(parseInt(e.target.value))}
              style={{ width: '300px' }}
            />
          </label>
        </div>
        
        <div>
          <label>
            旋转不变换 (Straighten): {straightenAngle}°
            <input
              type="range"
              min="-360"
              max="360"
              step="1"
              value={straightenAngle}
              onChange={(e) => handleStraightenChange(parseInt(e.target.value))}
              style={{ width: '300px' }}
            />
          </label>
        </div>
        
        <button onClick={resetRotation}>重置旋转</button>
      </div>
    </div>
  );
}
```

## Transform 与 Straighten 的区别

### Transform 方法

`transform` 方法在旋转画板时会进行以下操作：

1. 旋转所有形状和背景
2. 调整画板大小以适应旋转后的内容
3. 应用缩放以确保所有内容仍然可见
4. 保持旋转中心点不变

这种方法适用于创意编辑和视觉效果，因为它会保持所有内容在视图内且不会裁剪任何内容。

```js
// 旋转画板 45 度并变换
editor.rotation.transform(45);
```

### Straighten 方法

`straighten` 方法在旋转画板时：

1. 仅旋转内容，不进行缩放或变换
2. 保持画板大小不变
3. 可能导致部分内容在旋转后超出画板边界

这种方法适用于图像校正和简单旋转，例如将倾斜的照片调整为水平/垂直。

```js
// 旋转画板 -30 度而不变换
editor.rotation.straighten(-30);
```

## 形状旋转

除了整个画板的旋转外，您还可以旋转单个形状：

```js
// 创建一个形状
const triangle = editor.shapes.triangle.insert({
  x: 200,
  y: 200,
  radius: 50,
  fill: 'green'
});

// 旋转形状 60 度
triangle.rotate(60);

// 或者使用 rotation 方法
triangle.rotation(60);

// 获取当前旋转角度
const currentAngle = triangle.rotation();
console.log(`当前旋转角度: ${currentAngle}°`);
```

## 旋转中心点

在 Pikaso 中，旋转操作默认围绕形状或画板的中心点进行。对于形状，您可以使用 `offset` 属性来改变旋转中心点：

```js
// 创建一个矩形
const rect = editor.shapes.rect.insert({
  x: 100,
  y: 100,
  width: 200,
  height: 100,
  fill: 'blue'
});

// 设置偏移量以改变旋转中心点
rect.offset({
  x: 100,  // 旋转中心点的 X 坐标（相对于形状左上角）
  y: 50    // 旋转中心点的 Y 坐标（相对于形状左上角）
});

// 旋转形状
rect.rotation(45);
```

## 旋转与变换事件

您可以监听旋转和变换事件：

```js
// 监听旋转开始事件
editor.on('rotation:start', (data) => {
  console.log('旋转开始', data);
});

// 监听旋转过程事件
editor.on('rotation:progress', (data) => {
  console.log('旋转进行中', data);
});

// 监听旋转结束事件
editor.on('rotation:end', (data) => {
  console.log('旋转结束', data);
});

// 监听变换事件
editor.on('transform', (data) => {
  console.log('变换发生', data);
});
```

## 相关资源

- [旋转矩阵](https://en.wikipedia.org/wiki/Rotation_matrix) - 了解旋转的数学原理
- [计算旋转图像的大小](https://iiif.io/api/annex/notes/rotation/) - 如何计算旋转后的图像尺寸
- [Pikaso Transform 的工作原理](https://drive.google.com/file/d/1yhi1vG9a_U0rPpz57jWEi8DQJemrtjr9/view?usp=sharing) - 深入了解 Pikaso 中的变换机制
