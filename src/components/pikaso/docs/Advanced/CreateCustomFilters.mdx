# 创建自定义滤镜

除了内置的完整滤镜集外，Pikaso 还允许创建或重用自定义画布滤镜，并允许将多个滤镜合并为一个。

画布滤镜由多个开源项目提供，例如 [silvia-odwyer/pixels.js](https://github.com/silvia-odwyer/pixels.js) 和 [arahaya/ImageFilters.js](https://github.com/arahaya/ImageFilters.js)。

也可以查看 [https://www.html5rocks.com/en/tutorials/canvas/imagefilters/](https://www.html5rocks.com/en/tutorials/canvas/imagefilters/)

## 基本用法

```js
// pixels.js 示例 
const shape = editor.shapes.image.insert('<URL>')

shape.addFilter({
  customFn: imageData => PixelsJS.filterImgData(imageData, '<Filter Name>')
})

// 自定义滤镜
shape.addFilter({
  customFn: imageData => {
    const d = imageData.data

    for (let i = 0; i < d.length; i += 4) {
      const r = d[i]
      const g = d[i+1]
      const b = d[i+2]
      const v = 0.2126 * r + 0.7152 * g + 0.0722 * b
      d[i] = d[i+1] = d[i+2] = v
    }
  }
})
```

## 创建自定义滤镜

自定义滤镜实际上是一个处理图像数据的函数，它接收一个 `ImageData` 对象作为参数，并直接修改其像素数据。

以下是一些自定义滤镜的示例：

### 灰度滤镜

将图像转换为灰度：

```js
// 灰度滤镜
shape.addFilter({
  customFn: imageData => {
    const d = imageData.data

    for (let i = 0; i < d.length; i += 4) {
      const r = d[i]
      const g = d[i+1]
      const b = d[i+2]
      // 使用加权平均法计算灰度值
      const v = 0.2126 * r + 0.7152 * g + 0.0722 * b
      d[i] = d[i+1] = d[i+2] = v
    }
  }
})
```

### 亮度调整滤镜

调整图像亮度：

```js
// 亮度调整滤镜
shape.addFilter({
  customFn: imageData => {
    const d = imageData.data
    const adjustment = 100 // 正值增加亮度，负值降低亮度

    for (let i = 0; i < d.length; i += 4) {
      d[i] += adjustment     // 红色通道
      d[i + 1] += adjustment // 绿色通道
      d[i + 2] += adjustment // 蓝色通道
    }
  }
})
```

### 反色滤镜

创建图像的负片效果：

```js
// 反色滤镜
shape.addFilter({
  customFn: imageData => {
    const d = imageData.data

    for (let i = 0; i < d.length; i += 4) {
      d[i] = 255 - d[i]         // 红色通道反转
      d[i + 1] = 255 - d[i + 1] // 绿色通道反转
      d[i + 2] = 255 - d[i + 2] // 蓝色通道反转
    }
  }
})
```

### 单色滤镜

只保留特定颜色通道：

```js
// 红色单色滤镜
shape.addFilter({
  customFn: imageData => {
    const d = imageData.data

    for (let i = 0; i < d.length; i += 4) {
      // 保留红色通道，移除绿色和蓝色通道
      d[i + 1] = 0 // 绿色通道
      d[i + 2] = 0 // 蓝色通道
    }
  }
})
```

## 使用第三方库

您可以与第三方图像处理库集成，例如 [pixels.js](https://github.com/silvia-odwyer/pixels.js)：

```js
// 引入 pixels.js 库
import PixelsJS from 'pixels.js';

// 使用 pixels.js 的滤镜
shape.addFilter({
  customFn: imageData => PixelsJS.filterImgData(imageData, 'emboss')
});
```

或者使用 [ImageFilters.js](https://github.com/arahaya/ImageFilters.js)：

```js
// 引入 ImageFilters.js 库
import ImageFilters from 'imagefilters';

// 使用 ImageFilters 的滤镜
shape.addFilter({
  customFn: imageData => {
    return ImageFilters.GaussianBlur(imageData, 5);
  }
});
```

## 组合多个滤镜

您可以创建组合多个滤镜效果的复合滤镜：

```js
// 创建组合滤镜：灰度 + 亮度调整
shape.addFilter({
  customFn: imageData => {
    const d = imageData.data
    
    // 先应用灰度滤镜
    for (let i = 0; i < d.length; i += 4) {
      const r = d[i]
      const g = d[i+1]
      const b = d[i+2]
      const v = 0.2126 * r + 0.7152 * g + 0.0722 * b
      d[i] = d[i+1] = d[i+2] = v
    }
    
    // 再应用亮度调整滤镜
    const adjustment = 50
    for (let i = 0; i < d.length; i += 4) {
      d[i] += adjustment
      d[i + 1] += adjustment
      d[i + 2] += adjustment
    }
  }
})
```

## 应用滤镜到多个对象

您可以将相同的自定义滤镜应用到多个形状：

```js
// 创建自定义滤镜函数
const myCustomFilter = imageData => {
  const d = imageData.data
  
  for (let i = 0; i < d.length; i += 4) {
    const r = d[i]
    const g = d[i+1]
    const b = d[i+2]
    const v = 0.2126 * r + 0.7152 * g + 0.0722 * b
    d[i] = d[i+1] = d[i+2] = v
  }
};

// 应用到多个形状
const shape1 = editor.shapes.image.insert('/image1.jpg', { x: 100, y: 100 });
const shape2 = editor.shapes.image.insert('/image2.jpg', { x: 300, y: 100 });

shape1.addFilter({ customFn: myCustomFilter });
shape2.addFilter({ customFn: myCustomFilter });

// 或使用选择功能应用到多个选中的形状
editor.selection.addFilter({ customFn: myCustomFilter });
```

## 高级示例：创建可配置的滤镜

您可以创建具有配置选项的可重用滤镜：

```js
// 创建可配置的亮度调整滤镜
function createBrightnessFilter(adjustment) {
  return {
    customFn: imageData => {
      const d = imageData.data
      
      for (let i = 0; i < d.length; i += 4) {
        d[i] += adjustment
        d[i + 1] += adjustment
        d[i + 2] += adjustment
      }
    }
  };
}

// 使用不同配置应用滤镜
const shape1 = editor.shapes.image.insert('/image.jpg', { x: 100, y: 100 });
const shape2 = editor.shapes.image.insert('/image.jpg', { x: 300, y: 100 });

// 增加亮度
shape1.addFilter(createBrightnessFilter(50));

// 降低亮度
shape2.addFilter(createBrightnessFilter(-50));
```

## 性能考虑

自定义滤镜处理大图像时可能会影响性能。这里有一些优化建议：

1. 尽量简化滤镜算法，避免复杂计算
2. 考虑使用 Web Workers 处理大图像
3. 避免在动画循环中频繁应用滤镜
4. 考虑先将图像缩小，应用滤镜后再恢复原始大小

## 滤镜限制

自定义滤镜有以下限制：

1. 它们只能处理当前在画布上的像素
2. 不能访问原始图像的完整数据
3. 一些效果（如模糊）在图像边缘可能不理想
4. 滤镜应用后不能撤销其效果，只能移除滤镜或重新应用不同的滤镜
