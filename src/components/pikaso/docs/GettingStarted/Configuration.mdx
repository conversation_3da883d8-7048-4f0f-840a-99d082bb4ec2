# 配置

Pikaso 支持跨所有组件的全局配置设置。
可以使用预定义的设置初始化编辑器，并且稍后可以在每个组件中覆盖这些设置。

## API 参考
**[interfaces/OptionalSettings.html](/api/interfaces/OptionalSettings.html)**

## 默认设置
```json
{
  "disableCanvasContextMenu": true,
  "containerClassName": "pikaso",
  "transformer": {
    "borderDash": [15, 10],
    "borderStroke": "#fff",
    "borderStrokeWidth": 3,
    "anchorSize": 15,
    "anchorColor": "#fff",
    "anchorStroke": "#fff",
    "anchorBorderWidth": 1,
    "anchorCornerRadius": 30
  },
  "cropper": {
    "transformer": {
      "borderDash": [15, 10],
      "borderStroke": "#fff",
      "borderStrokeWidth": 3,
      "anchorSize": 15,
      "anchorColor": "#fff",
      "anchorStroke": "#fff",
      "anchorBorderWidth": 1,
      "anchorCornerRadius": 30
    },
    "circular": false,
    "fixed": false,
    "keepRatio": true,
    "aspectRatio": 1,
    "minWidth": 100,
    "minHeight": 100,
    "marginRatio": 1.1,
    "overlay": {
      "color": "#262626",
      "opacity": 0.5
    },
    "guides": {
      "show": true,
      "count": 3,
      "color": "#fff",
      "width": 1,
      "dash": [15, 10]
    }
  },
  "drawing": {
    "autoSelect": false,
    "keyboard": {
      "cancelOnEscape": true
    }
  },
  "selection": {
    "interactive": true,
    "keyboard": {
      "enabled": true,
      "movingSpaces": 5,
      "map": {
        "delete": ["Backspace", "Delete"],
        "moveLeft": ["ArrowLeft"],
        "moveRight": ["ArrowRight"],
        "moveUp": ["ArrowUp"],
        "moveDown": ["ArrowDown"],
        "deselect": ["Escape"]
      }
    },
    "transformer": {
      "borderStroke": "#fff",
      "borderStrokeWidth": 3,
      "anchorSize": 15,
      "anchorColor": "#fff",
      "anchorStroke": "#fff",
      "anchorBorderWidth": 1,
      "anchorCornerRadius": 30,
      "borderDash": [0, 0]
    },
    "zone": {
      "fill": "rgba(105, 105, 105, 0.7)",
      "stroke": "#dbdbdb"
    }
  },
  "history": {
    "keyboard": {
      "enabled": true
    }
  }
}
```

## 使用方法
```js
import Pikaso from 'pikaso'

const editor = new Pikaso({
  container: document.getElementById('<YOUR_DIV_ID>'),
  disableCanvasContextMenu: false,
  containerClassName: 'foo',
  selection: {
    interactive: false,
  },
  // 其他设置
})
```

## 覆盖设置
```js
import Pikaso from 'pikaso'

const editor = new Pikaso({
  container: document.getElementById('<YOUR_DIV_ID>'),
  transformer: {
    borderStroke: 'green',
    borderStrokeWidth: 1
  }
})

editor.cropper.start({
  transformer: {
    borderStroke: 'yellow',
    borderStrokeWidth: 2
  }
})
```

## 配置说明

以下是主要配置选项的详细说明：

### 基础选项

- **`disableCanvasContextMenu`**: 是否禁用画布右键菜单（默认: `true`）
- **`containerClassName`**: 容器元素的CSS类名（默认: `'pikaso'`）

### 变换器选项（transformer）

变换器定义了当对象被选择时，周围出现的变换控件的样式：

```js
transformer: {
  borderDash: [15, 10],      // 边框虚线样式
  borderStroke: '#fff',      // 边框颜色
  borderStrokeWidth: 3,      // 边框宽度
  anchorSize: 15,            // 锚点大小
  anchorColor: '#fff',       // 锚点颜色
  anchorStroke: '#fff',      // 锚点边框颜色
  anchorBorderWidth: 1,      // 锚点边框宽度
  anchorCornerRadius: 30     // 锚点圆角半径
}
```

### 裁剪器选项（cropper）

裁剪器配置定义了裁剪工具的行为和外观：

```js
cropper: {
  // 裁剪区域变换器样式
  transformer: { /* 变换器样式配置 */ },
  
  // 裁剪类型
  circular: false,    // 是否为圆形裁剪
  fixed: false,       // 是否为固定尺寸裁剪
  
  // 尺寸约束
  keepRatio: true,    // 是否保持比例
  aspectRatio: 1,     // 宽高比（1表示正方形）
  minWidth: 100,      // 最小宽度
  minHeight: 100,     // 最小高度
  marginRatio: 1.1,   // 边距比例
  
  // 覆盖层样式
  overlay: {
    color: '#262626',  // 覆盖层颜色
    opacity: 0.5       // 覆盖层透明度
  },
  
  // 辅助线设置
  guides: {
    show: true,        // 是否显示辅助线
    count: 3,          // 辅助线数量（每行/列）
    color: '#fff',     // 辅助线颜色
    width: 1,          // 辅助线宽度
    dash: [15, 10]     // 辅助线虚线样式
  }
}
```

### 绘图选项（drawing）

绘图配置控制绘图工具的行为：

```js
drawing: {
  autoSelect: false,   // 绘制完成后是否自动选择
  keyboard: {
    cancelOnEscape: true  // 是否可以通过ESC键取消绘制
  }
}
```

### 选择选项（selection）

选择配置定义了选择功能的行为和视觉效果：

```js
selection: {
  interactive: true,   // 是否启用交互式选择
  
  // 键盘交互设置
  keyboard: {
    enabled: true,     // 是否启用键盘快捷键
    movingSpaces: 5,   // 每次按方向键移动的像素数
    map: {
      delete: ['Backspace', 'Delete'],  // 删除选中对象的按键
      moveLeft: ['ArrowLeft'],          // 向左移动的按键
      moveRight: ['ArrowRight'],        // 向右移动的按键
      moveUp: ['ArrowUp'],              // 向上移动的按键
      moveDown: ['ArrowDown'],          // 向下移动的按键
      deselect: ['Escape']              // 取消选择的按键
    }
  },
  
  // 选择变换器样式
  transformer: { /* 变换器样式配置 */ },
  
  // 区域选择样式
  zone: {
    fill: 'rgba(105, 105, 105, 0.7)',  // 填充颜色
    stroke: '#dbdbdb'                  // 边框颜色
  }
}
```

### 历史记录选项（history）

历史记录配置控制撤销/重做功能：

```js
history: {
  keyboard: {
    enabled: true  // 是否启用历史记录的键盘快捷键
  }
}
```

## 使用局部覆盖

您可以在特定操作中覆盖全局配置：

```js
// 全局配置蓝色边框
const editor = new Pikaso({
  container: document.getElementById('editor'),
  transformer: {
    borderStroke: 'blue',
    borderStrokeWidth: 2
  }
})

// 在裁剪操作中使用红色边框
editor.cropper.start({
  transformer: {
    borderStroke: 'red',
    borderStrokeWidth: 3
  }
})

// 形状绘制时使用绿色
editor.shapes.circle.draw({
  stroke: 'green',
  strokeWidth: 2
})
