# Pikaso

Pikaso 是一个功能强大的 JavaScript 画布编辑器库，可以让您轻松创建和操作交互式图像编辑器。

## 简介

Pikaso 提供了丰富的功能集，用于构建复杂的图像编辑应用程序：

- 形状绘制和操作
- 图像导入和导出
- 选择和变换
- 滤镜应用
- 历史记录（撤销/重做）
- 旋转和翻转
- 分组管理
- 事件系统
- 以及更多...

无论是简单的图片裁剪工具，还是复杂的图形设计应用，Pikaso 都提供了必要的工具和 API，让您的开发过程更加简单和高效。

## 主要特性

### 丰富的形状支持

Pikaso 支持多种内置形状，包括：

- 矩形
- 圆形
- 椭圆
- 三角形
- 多边形
- 线条
- 箭头
- 文本
- 标签
- 图像
- SVG 路径

### 强大的选择和变换

Pikaso 提供强大的选择和变换功能，允许您：

- 选择单个或多个形状
- 移动、缩放和旋转形状
- 使用键盘快捷键控制选中的形状
- 应用自定义边框和锚点样式

### 灵活的事件系统

Pikaso 基于事件驱动的架构，让您可以：

- 监听形状和背景的变化
- 响应用户交互
- 创建自定义行为和交互

### 完整的状态管理

Pikaso 内置了历史记录功能，支持：

- 撤销/重做操作
- 状态跳转
- 保存和恢复编辑器状态

### 图像导入和导出

Pikaso 支持多种格式的图像导入和导出：

- 从 URL 或文件加载图像
- 导出为 PNG、JPEG 或 DataURL
- 自定义导出质量和尺寸

## 浏览文档

要深入了解 Pikaso，请查看以下文档部分：

- [安装指南](/getting-started/installation) - 了解如何将 Pikaso 添加到您的项目中
- [使用指南](/getting-started/usage) - 基本用法和框架集成
- [配置选项](/getting-started/configuration) - 自定义 Pikaso 以满足您的需求
- [核心组件](/core) - 探索 Pikaso 的主要功能和组件
- [高级主题](/advanced) - 学习自定义形状、滤镜和 Node.js 支持
- [实用工具](/utilities) - 使用辅助函数简化常见任务

## 在线示例

您可以访问 [Pikaso 示例页面](https://pikasojs.com/examples) 查看各种交互式演示，了解 Pikaso 的功能和用法。

## 贡献

Pikaso 是一个开源项目，欢迎贡献！如果您想参与，请访问 [GitHub 仓库](https://github.com/pikasojs/pikaso)。

## 许可

Pikaso 在 MIT 许可下发布。详情请查看 [LICENSE](https://github.com/pikasojs/pikaso/blob/master/LICENSE) 文件。
