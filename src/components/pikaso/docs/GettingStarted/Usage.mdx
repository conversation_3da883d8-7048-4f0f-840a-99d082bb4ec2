# 使用方法

## 基本用法

```js
import Pikaso from 'pikaso'

const editor = new Pikaso({
  container: document.getElementById('<YOUR_DIV_ID>')
})
```

## React

使用官方 Hook 在 React 中使用 Pikaso 非常简单

[官方 React Hook](https://github.com/pikasojs/pikaso-react-hook)

### 安装 Hook

```bash
npm install @pikaso/react-hook --save
```

### 使用 Hook

```jsx
import { usePikaso } from '@pikaso/react-hook';

function PikasoEditor() {
  const [ref, editor] = usePikaso({
    // 配置选项
  });

  // 在编辑器实例加载后使用它
  useEffect(() => {
    if (!editor) return;

    // 操作 editor
    editor.shapes.circle.insert({
      x: 100,
      y: 100,
      radius: 50,
      fill: 'red'
    });
  }, [editor]);

  return <div ref={ref} style={{ width: '100%', height: '500px' }} />;
}
```

---

或者，您可以编写自己的 React Hook。

### TypeScript 示例

```tsx
import { useRef, useState, useEffect, RefObject } from 'react'
import Pikaso, { Settings } from 'pikaso'

type Nullable<T> = T | null;

export default function usePikaso(
  options: Partial<Settings> = {}
): [RefObject<HTMLDivElement>, Pikaso | null] {
  const [instance, setInstance] = useState<Nullable<Pikaso>>(null)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!ref.current) return;
    
    const editor = new Pikaso({
      container: ref.current,
      ...options
    })

    setInstance(editor)
    
    // 清理函数
    return () => {
      // 如果需要，在此处进行清理
    };
  }, [])

  return [ref, instance]
}
```

### JavaScript 示例

```jsx
import { useRef, useState, useEffect } from 'react'
import Pikaso from 'pikaso'

export default function usePikaso(options = {}) {
  const [instance, setInstance] = useState(null)
  const ref = useRef(null)

  useEffect(() => {
    if (!ref.current) return;
    
    const editor = new Pikaso({
      container: ref.current,
      ...options
    })

    setInstance(editor)
    
    // 清理函数
    return () => {
      // 如果需要，在此处进行清理
    };
  }, [])

  return [ref, instance]
}
```

## 完整 React 示例

```jsx
import React, { useEffect, useState } from 'react';
import { usePikaso } from '@pikaso/react-hook';

function PikasoEditor() {
  const [ref, editor] = usePikaso({
    selection: {
      transformer: {
        borderStroke: '#262626',
        anchorFill: '#262626'
      }
    }
  });
  
  const [mode, setMode] = useState('select');
  
  useEffect(() => {
    if (!editor) return;
    
    // 加载一个背景图像
    editor.loadFromUrl('https://example.com/background.jpg')
      .then(() => {
        console.log('背景图像已加载');
      })
      .catch(error => {
        console.error('加载图像时出错:', error);
      });
      
    // 添加一些初始形状
    editor.shapes.circle.insert({
      x: 200,
      y: 200,
      radius: 50,
      fill: 'red',
      draggable: true
    });
    
    editor.shapes.rect.insert({
      x: 400,
      y: 200,
      width: 100,
      height: 80,
      fill: 'blue',
      draggable: true
    });
  }, [editor]);
  
  // 切换绘图模式
  const startDrawing = (shape) => {
    if (!editor) return;
    
    // 停止任何正在进行的绘制
    editor.board.drawing.cancel();
    
    setMode(shape);
    
    // 开始绘制所选形状
    switch (shape) {
      case 'circle':
        editor.shapes.circle.draw({
          fill: 'green'
        });
        break;
      case 'rect':
        editor.shapes.rect.draw({
          fill: 'purple'
        });
        break;
      case 'triangle':
        editor.shapes.triangle.draw({
          fill: 'orange'
        });
        break;
      default:
        setMode('select');
        break;
    }
  };
  
  // 清除所有形状
  const clearAll = () => {
    if (!editor) return;
    
    editor.board.shapes.forEach(shape => {
      shape.remove();
    });
  };
  
  // 下载图像
  const downloadImage = () => {
    if (!editor) return;
    
    const dataURL = editor.export({
      mimeType: 'image/png',
      quality: 1
    });
    
    const link = document.createElement('a');
    link.href = dataURL;
    link.download = 'pikaso-image.png';
    link.click();
  };

  return (
    <div style={{ width: '100%', maxWidth: '1000px', margin: '0 auto' }}>
      <div ref={ref} style={{ width: '100%', height: '500px', border: '1px solid #ccc' }} />
      
      <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'center', gap: '10px' }}>
        <button 
          onClick={() => setMode('select')} 
          style={{ background: mode === 'select' ? '#262626' : '#f0f0f0', color: mode === 'select' ? 'white' : 'black' }}
        >
          选择模式
        </button>
        <button 
          onClick={() => startDrawing('circle')} 
          style={{ background: mode === 'circle' ? '#262626' : '#f0f0f0', color: mode === 'circle' ? 'white' : 'black' }}
        >
          绘制圆形
        </button>
        <button 
          onClick={() => startDrawing('rect')} 
          style={{ background: mode === 'rect' ? '#262626' : '#f0f0f0', color: mode === 'rect' ? 'white' : 'black' }}
        >
          绘制矩形
        </button>
        <button 
          onClick={() => startDrawing('triangle')} 
          style={{ background: mode === 'triangle' ? '#262626' : '#f0f0f0', color: mode === 'triangle' ? 'white' : 'black' }}
        >
          绘制三角形
        </button>
        <button onClick={clearAll}>清除全部</button>
        <button onClick={downloadImage}>下载图像</button>
      </div>
    </div>
  );
}

export default PikasoEditor;
```

## Vue

在 Vue 中使用 Pikaso 也很直接，下面是一个 Vue 3 的简单示例：

```vue
<template>
  <div>
    <div ref="editorRef" style="width: 100%; height: 500px; border: 1px solid #ccc;"></div>
    
    <div style="margin-top: 10px; display: flex; justify-content: center; gap: 10px;">
      <button @click="setMode('select')" :class="{ active: mode === 'select' }">选择模式</button>
      <button @click="startDrawing('circle')" :class="{ active: mode === 'circle' }">绘制圆形</button>
      <button @click="startDrawing('rect')" :class="{ active: mode === 'rect' }">绘制矩形</button>
      <button @click="clearAll">清除全部</button>
      <button @click="downloadImage">下载图像</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import Pikaso from 'pikaso';

export default {
  name: 'PikasoEditor',
  setup() {
    const editorRef = ref(null);
    const editor = ref(null);
    const mode = ref('select');

    onMounted(() => {
      if (editorRef.value) {
        // 创建编辑器实例
        editor.value = new Pikaso({
          container: editorRef.value,
          selection: {
            transformer: {
              borderStroke: '#262626',
              anchorFill: '#262626'
            }
          }
        });

        // 添加初始形状
        editor.value.shapes.circle.insert({
          x: 200,
          y: 200,
          radius: 50,
          fill: 'red',
          draggable: true
        });
      }
    });

    onBeforeUnmount(() => {
      // 清理
      editor.value = null;
    });

    const startDrawing = (shape) => {
      if (!editor.value) return;
      
      // 停止任何正在进行的绘制
      editor.value.board.drawing.cancel();
      
      mode.value = shape;
      
      // 开始绘制所选形状
      switch (shape) {
        case 'circle':
          editor.value.shapes.circle.draw({
            fill: 'green'
          });
          break;
        case 'rect':
          editor.value.shapes.rect.draw({
            fill: 'purple'
          });
          break;
        default:
          mode.value = 'select';
          break;
      }
    };
    
    const setMode = (newMode) => {
      mode.value = newMode;
      
      if (newMode === 'select' && editor.value) {
        editor.value.board.drawing.cancel();
      }
    };
    
    const clearAll = () => {
      if (!editor.value) return;
      
      editor.value.board.shapes.forEach(shape => {
        shape.remove();
      });
    };
    
    const downloadImage = () => {
      if (!editor.value) return;
      
      const dataURL = editor.value.export({
        mimeType: 'image/png',
        quality: 1
      });
      
      const link = document.createElement('a');
      link.href = dataURL;
      link.download = 'pikaso-image.png';
      link.click();
    };

    return {
      editorRef,
      mode,
      startDrawing,
      setMode,
      clearAll,
      downloadImage
    };
  }
};
</script>

<style scoped>
button {
  padding: 5px 10px;
  background: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button.active {
  background: #262626;
  color: white;
}
</style>
```

## Vue Composition API 示例

使用 Vue 3 的 Composition API 创建可重用的 Pikaso 组合式函数：

```js
// usePikaso.js
import { ref, onMounted, onBeforeUnmount } from 'vue';
import Pikaso from 'pikaso';

export function usePikaso(options = {}) {
  const editorRef = ref(null);
  const editor = ref(null);

  onMounted(() => {
    if (editorRef.value) {
      editor.value = new Pikaso({
        container: editorRef.value,
        ...options
      });
    }
  });

  onBeforeUnmount(() => {
    editor.value = null;
  });

  return {
    editorRef,
    editor
  };
}
```

使用这个组合式函数：

```vue
<template>
  <div>
    <div ref="editorRef" style="width: 100%; height: 500px;"></div>
    <button @click="addCircle">添加圆形</button>
  </div>
</template>

<script>
import { watch } from 'vue';
import { usePikaso } from './usePikaso';

export default {
  setup() {
    const { editorRef, editor } = usePikaso({
      selection: {
        transformer: {
          borderStroke: '#262626',
          anchorFill: '#262626'
        }
      }
    });

    watch(editor, (newEditor) => {
      if (newEditor) {
        // 编辑器初始化完成后的操作
        console.log('Pikaso 编辑器已初始化');
      }
    });

    const addCircle = () => {
      if (editor.value) {
        editor.value.shapes.circle.insert({
          x: 200,
          y: 200,
          radius: 50,
          fill: 'red'
        });
      }
    };

    return {
      editorRef,
      addCircle
    };
  }
};
</script>
```

## 在 Angular 中使用

```typescript
// pikaso.service.ts
import { Injectable } from '@angular/core';
import Pikaso from 'pikaso';

@Injectable({
  providedIn: 'root'
})
export class PikasoService {
  createEditor(container: HTMLElement, options: any = {}) {
    return new Pikaso({
      container,
      ...options
    });
  }
}
```

```typescript
// pikaso-editor.component.ts
import { Component, ElementRef, OnInit, ViewChild, OnDestroy } from '@angular/core';
import Pikaso from 'pikaso';
import { PikasoService } from './pikaso.service';

@Component({
  selector: 'app-pikaso-editor',
  template: `
    <div class="editor-container" #editorContainer></div>
    <div class="toolbar">
      <button (click)="addCircle()">添加圆形</button>
      <button (click)="addRect()">添加矩形</button>
      <button (click)="clear()">清除</button>
    </div>
  `,
  styles: [`
    .editor-container {
      width: 100%;
      height: 500px;
      border: 1px solid #ccc;
    }
    .toolbar {
      margin-top: 10px;
      display: flex;
      gap: 10px;
    }
  `]
})
export class PikasoEditorComponent implements OnInit, OnDestroy {
  @ViewChild('editorContainer', { static: true }) editorContainer!: ElementRef;
  private editor: Pikaso | null = null;

  constructor(private pikasoService: PikasoService) {}

  ngOnInit() {
    this.editor = this.pikasoService.createEditor(this.editorContainer.nativeElement, {
      selection: {
        transformer: {
          borderStroke: '#262626',
          anchorFill: '#262626'
        }
      }
    });
  }

  ngOnDestroy() {
    // 清理资源
    this.editor = null;
  }

  addCircle() {
    if (this.editor) {
      this.editor.shapes.circle.insert({
        x: 200,
        y: 200,
        radius: 50,
        fill: 'red'
      });
    }
  }

  addRect() {
    if (this.editor) {
      this.editor.shapes.rect.insert({
        x: 300,
        y: 200,
        width: 100,
        height: 80,
        fill: 'blue'
      });
    }
  }

  clear() {
    if (this.editor) {
      this.editor.board.shapes.forEach(shape => {
        shape.remove();
      });
    }
  }
}
```

## 更多特性和示例

查看 [Pikaso API 文档](/api/index.html) 了解更多高级功能和用法。

你还可以尝试 [Vue 示例](https://codesandbox.io/s/vue3-example-o3cig)，了解更多实际使用场景。
