import {
	Drawer,
	Drawer<PERSON>ontent,
	DrawerHeader,
	DrawerTitle,
} from "@/components/ui/drawer";
import {
	ColorPicker,
	ColorPicker<PERSON><PERSON>pha,
	ColorPickerEyeDropper,
	ColorPickerFormat,
	ColorPickerHue,
	ColorPickerOutput,
	ColorPickerSelection,
} from "@/components/ui/kibo-ui/color-picker";
import { useState } from "react";
interface BackgroundDrawerProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onBackgroundChange: (type: "color" | "image", value: string) => void;
}

export const BackgroundDrawer = ({
	open,
	onOpenChange,
	onBackgroundChange,
}: BackgroundDrawerProps) => {
	const [selectedTab, setSelectedTab] = useState<"color" | "image">("color");
	const [imageUrl, setImageUrl] = useState("");

	const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (!file) return;

		const reader = new FileReader();
		reader.onload = (event) => {
			const url = event.target?.result as string;
			setImageUrl(url);
			onBackgroundChange("image", url);
		};
		reader.readAsDataURL(file);
	};

	const colorArrayToHex = (colorArray: number[]): string => {
		if (colorArray.length < 3) return "#ffffff"; // Default white if invalid array

		// Extract RGB values and convert to hex
		const r = Math.round(colorArray[0]);
		const g = Math.round(colorArray[1]);
		const b = Math.round(colorArray[2]);

		// Convert RGB to hex
		return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
	};

	return (
		<Drawer open={open} onOpenChange={onOpenChange}>
			<DrawerContent>
				<DrawerHeader>
					<DrawerTitle>Change Background</DrawerTitle>
				</DrawerHeader>

				<div className="p-4">
					<div className="flex border-b mb-4">
						<button
							type="button"
							className={`px-4 py-2 ${selectedTab === "color" ? "border-b-2 border-primary" : ""}`}
							onClick={() => setSelectedTab("color")}
						>
							Color
						</button>
						<button
							type="button"
							className={`px-4 py-2 ${selectedTab === "image" ? "border-b-2 border-primary" : ""}`}
							onClick={() => setSelectedTab("image")}
						>
							Image
						</button>
					</div>

					{selectedTab === "color" ? (
						<ColorPicker
							className="w-full max-w-[300px] rounded-md border bg-background p-4 shadow-sm"
							onChange={(color) => {
								if (
									Array.isArray(color) &&
									color.every((c) => typeof c === "number")
								) {
									onBackgroundChange("color", colorArrayToHex(color));
								}
								console.log("color", color);
							}}
						>
							<ColorPickerSelection />
							<div className="flex items-center gap-4">
								<ColorPickerEyeDropper />
								<div className="w-full grid gap-1">
									<ColorPickerHue />
									<ColorPickerAlpha />
								</div>
							</div>
							<div className="flex items-center gap-2">
								<ColorPickerOutput />
								<ColorPickerFormat />
							</div>
						</ColorPicker>
					) : (
						<div>
							<input
								type="file"
								accept="image/*"
								onChange={handleImageUpload}
								className="mb-4"
							/>
							{imageUrl && (
								<div className="mt-2">
									<img src={imageUrl} alt="Preview" className="max-h-40" />
								</div>
							)}
						</div>
					)}
				</div>
			</DrawerContent>
		</Drawer>
	);
};
