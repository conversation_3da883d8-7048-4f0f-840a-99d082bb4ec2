import type { LabelModel, ShapeModel } from "@/components/pikaso/index.all"
import type { <PERSON>kaso } from "@/components/pikaso/index.all"
import type Konva from "konva"
import type { RefObject } from "react"
import { create } from "zustand"

interface CanvasSize {
  width: number
  height: number
}

// 相册页面数据结构
export interface AlbumPage {
  id: string
  name: string
  thumbnail: string // base64 图片数据
  canvasData: string // 序列化的画布数据
  createdAt: Date
  updatedAt: Date
}

// 相册管理状态
interface AlbumState {
  pages: AlbumPage[]
  currentPageId: string | null
  isLoading: boolean

  // 相册操作
  addPage: (name?: string) => string
  deletePage: (pageId: string) => void
  updatePage: (pageId: string, updates: Partial<AlbumPage>) => void
  reorderPages: (fromIndex: number, toIndex: number) => void
  setCurrentPage: (pageId: string | null) => void
  savePage: (pageId: string, canvasData: string, thumbnail: string) => void
  loadPage: (pageId: string) => AlbumPage | null
  generateThumbnail: () => Promise<string>
}

interface EditorState extends AlbumState {
  // 编辑器状态
  editor: Pikaso | null
  ref: RefObject<HTMLDivElement> | null
  canvasSize: CanvasSize
  selectedShapes: ShapeModel[]
  fileInputRef: RefObject<HTMLInputElement> | null

  // 背景面板状态
  backgroundPanelOpen: boolean

  // 属性面板状态
  propertyPanelOpen: boolean

  // 移动设备状态
  isMobile: boolean

  // 设置函数
  setEditor: (editor: Pikaso | null) => void
  setRef: (ref: RefObject<HTMLDivElement>) => void
  setCanvasSize: (size: CanvasSize) => void
  setSelectedShapes: (shapes: ShapeModel[]) => void
  setFileInputRef: (ref: RefObject<HTMLInputElement>) => void
  setBackgroundPanelOpen: (open: boolean) => void
  setPropertyPanelOpen: (open: boolean) => void
  setIsMobile: (isMobile: boolean) => void

  // 操作函数
  addImage: (
    src: string,
    options?: { x?: number; y?: number },
  ) => Promise<ShapeModel> | undefined
  addText: (
    text: string,
    options?: { x?: number; y?: number; fontSize?: number; fill?: string },
  ) => ShapeModel | undefined
  addShape: (
    type: "rect" | "circle" | "triangle",
    options?: {
      x?: number
      y?: number
      width?: number
      height?: number
      radius?: number
      fill?: string
      stroke?: string
      strokeWidth?: number
    },
  ) => ShapeModel | undefined
  setBackground: (type: "color" | "image", value: string) => Promise<void>
  deleteSelected: () => void
  duplicateSelected: () => void
  triggerImageUpload: () => void
  changeLayerOrder: (action: "up" | "down" | "top" | "bottom") => void
  undo: () => void
  redo: () => void
  reset: () => void

  // 初始化函数
  initializeEditor: (options?: {
    initialWidth?: number
    initialHeight?: number
    autoResize?: boolean
  }) => void

  // 响应式调整画布大小
  resizeCanvas: () => void
}

// 计算初始画布大小的辅助函数
const getInitialCanvasSize = (
  initialWidth = 800,
  initialHeight = 600,
): CanvasSize => {
  if (typeof window === "undefined") {
    return { width: initialWidth, height: initialHeight }
  }

  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const isMobile = viewportWidth < 768
  const toolbarHeight = isMobile ? 140 : 120
  const sidebarWidth = isMobile ? 0 : 256
  const padding = isMobile ? 20 : 80

  const availableWidth = viewportWidth - sidebarWidth - padding
  const availableHeight = viewportHeight - toolbarHeight - padding

  const scaleX = availableWidth / initialWidth
  const scaleY = availableHeight / initialHeight
  const maxScale = isMobile ? 0.8 : 0.8
  const scale = Math.min(scaleX, scaleY, maxScale)

  const minWidth = isMobile ? 320 : 400
  const minHeight = isMobile ? 240 : 320

  return {
    width: Math.max(initialWidth * scale, minWidth),
    height: Math.max(initialHeight * scale, minHeight),
  }
}

export const useEditorStore = create<EditorState>((set, get) => ({
  // 初始状态
  editor: null,
  ref: null,
  canvasSize: getInitialCanvasSize(),
  selectedShapes: [],
  fileInputRef: null,
  backgroundPanelOpen: false,
  propertyPanelOpen: false,
  isMobile: typeof window !== "undefined" ? window.innerWidth < 768 : false,

  // 相册状态
  pages: [],
  currentPageId: null,
  isLoading: false,

  // 设置函数
  setEditor: (editor) => set({ editor }),
  setRef: (ref) => set({ ref }),
  setCanvasSize: (size) => set({ canvasSize: size }),
  setSelectedShapes: (shapes) => set({ selectedShapes: shapes }),
  setFileInputRef: (ref) => set({ fileInputRef: ref }),
  setBackgroundPanelOpen: (open) => set({ backgroundPanelOpen: open }),
  setPropertyPanelOpen: (open) => set({ propertyPanelOpen: open }),
  setIsMobile: (isMobile) => set({ isMobile }),

  // 初始化编辑器
  initializeEditor: (options = {}) => {
    const { initialWidth = 800, initialHeight = 600 } = options
    set({ canvasSize: getInitialCanvasSize(initialWidth, initialHeight) })

    // 检测是否为移动设备
    const checkMobile = () => {
      set({ isMobile: window.innerWidth < 768 })
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    // 注意：这里不能返回清理函数，因为这不是React hook
    // 实际使用时需要在组件中处理事件监听的清理
  },

  // 响应式调整画布大小
  resizeCanvas: () => {
    const { editor, canvasSize } = get()
    if (!editor) return

    const initialWidth = 800
    const initialHeight = 600

    // 获取视口尺寸
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 响应式计算空间占用
    const isMobile = viewportWidth < 768
    const toolbarHeight = isMobile ? 140 : 120
    const sidebarWidth = isMobile ? 0 : 256
    const padding = isMobile ? 20 : 80

    const availableWidth = viewportWidth - sidebarWidth - padding
    const availableHeight = viewportHeight - toolbarHeight - padding

    // 计算缩放比例
    const scaleX = availableWidth / initialWidth
    const scaleY = availableHeight / initialHeight
    const maxScale = isMobile ? 0.8 : 0.8
    const scale = Math.min(scaleX, scaleY, maxScale)

    // 设置最小尺寸
    const minWidth = isMobile ? 320 : 400
    const minHeight = isMobile ? 240 : 320

    const newSize = {
      width: Math.max(initialWidth * scale, minWidth),
      height: Math.max(initialHeight * scale, minHeight),
    }

    // 只有尺寸真正改变时才更新
    if (
      Math.abs(newSize.width - canvasSize.width) > 5 ||
      Math.abs(newSize.height - canvasSize.height) > 5
    ) {
      set({ canvasSize: newSize })
      editor.board.stage.size(newSize)
    }
  },

  // 添加图片
  addImage: (src, options) => {
    const { editor } = get()
    if (!editor) return

    const { x = 100, y = 100 } = options || {}

    return editor.shapes.image.insert(src, {
      x,
      y,
    })
  },

  // 添加文本
  addText: (text, options) => {
    const { editor } = get()
    if (!editor) return

    const { x = 100, y = 100, fontSize = 24, fill = "#000000" } = options || {}

    return editor.shapes.label.insert({
      container: { x, y },
      tag: {
        fill: "transparent",
        cornerRadius: 0,
      },
      text: {
        text,
        fontSize,
        fill,
      },
    })
  },

  // 添加形状
  addShape: (type, options) => {
    const { editor } = get()
    if (!editor) return

    const defaultOptions = {
      x: 100,
      y: 100,
      fill: "#3b82f6",
    }

    switch (type) {
      case "rect":
        return editor.shapes.rect.insert({
          ...defaultOptions,
          width: 100,
          height: 100,
          ...options,
        })
      case "circle":
        return editor.shapes.circle.insert({
          ...defaultOptions,
          radius: 50,
          ...options,
        })
      case "triangle":
        return editor.shapes.triangle.insert({
          ...defaultOptions,
          radius: 50,
          ...options,
        })
    }
  },

  // 设置背景
  setBackground: async (type, value) => {
    console.log("🚀 ~ type:", type)
    console.log("🚀 ~ value:", value)
    const { editor } = get()
    if (!editor) return

    if (type === "color") {
      editor.board.background.fill(value)
    } else if (type === "image") {
      try {
        await editor.board.background.setImageFromUrl(value, {
          size: "cover",
        })
      } catch (error) {
        console.error("设置背景图片失败:", error)
      }
    }
  },

  // 删除选中的形状
  deleteSelected: () => {
    const { editor, selectedShapes } = get()
    if (!editor || selectedShapes.length === 0) return

    editor.selection.delete()
    set({ selectedShapes: [] })
    editor.board.layer.draw()
  },

  // 复制选中的形状
  duplicateSelected: () => {
    const { editor, selectedShapes } = get()
    if (!editor || selectedShapes.length === 0) return

    // 存储新创建的形状，以便后续选择
    const newShapes: ShapeModel[] = []

    for (const shape of selectedShapes) {
      // 获取形状类型
      const className = shape.node.getClassName()

      // 创建新的位置，偏移20像素
      const newX = shape.node.x() + 20
      const newY = shape.node.y() + 20

      // 根据形状类型创建新的形状
      try {
        let newShape: ShapeModel | null = null

        switch (className) {
          case "Rect": {
            // 使用类型断言确保node是Konva.Rect类型
            const rectNode = shape.node as Konva.Rect

            // 获取矩形特有的属性
            const rectWidth = rectNode.width()
            const rectHeight = rectNode.height()
            const rectFill = rectNode.fill()
            const rectStroke = rectNode.stroke()
            const rectStrokeWidth = rectNode.strokeWidth()

            newShape = editor.shapes.rect.insert({
              x: newX,
              y: newY,
              width: rectWidth,
              height: rectHeight,
              fill: rectFill,
              stroke: rectStroke,
              strokeWidth: rectStrokeWidth,
            })
            break
          }

          case "Circle": {
            // 使用类型断言确保node是Konva.Circle类型
            const circleNode = shape.node as Konva.Circle

            // 获取圆形特有的属性
            const radius = circleNode.radius()
            const circleFill = circleNode.fill()
            const circleStroke = circleNode.stroke()
            const circleStrokeWidth = circleNode.strokeWidth()

            newShape = editor.shapes.circle.insert({
              x: newX,
              y: newY,
              radius: radius,
              fill: circleFill,
              stroke: circleStroke,
              strokeWidth: circleStrokeWidth,
            })
            break
          }

          // 可以根据需要添加更多形状类型
          default:
            console.log("未处理的形状类型:", className)
            break
        }

        if (newShape) {
          newShapes.push(newShape)
        }
      } catch (error) {
        console.error("复制形状时出错:", error)
      }
    }

    // 如果有新创建的形状，选中它们
    if (newShapes.length > 0) {
      editor.selection.multi(newShapes)
    }

    // 更新画布
    editor.board.layer.draw()
  },

  // 触发图片上传
  triggerImageUpload: () => {
    const { fileInputRef } = get()
    fileInputRef?.current?.click()
  },

  // 调整图层顺序
  changeLayerOrder: (action) => {
    const { editor, selectedShapes } = get()
    if (!editor || selectedShapes.length === 0) return

    for (const shape of selectedShapes) {
      switch (action) {
        case "up":
          shape.node.moveUp()
          break
        case "down":
          shape.node.moveDown()
          break
        case "top":
          shape.node.moveToTop()
          break
        case "bottom":
          shape.node.moveToBottom()
          break
      }
    }

    // 更新画布
    editor.board.layer.draw()
  },

  // 撤销
  undo: () => {
    const { editor } = get()
    editor?.undo()
  },

  // 重做
  redo: () => {
    const { editor } = get()
    editor?.redo()
  },

  // 重置
  reset: () => {
    const { editor } = get()
    editor?.reset()
  },

  // 相册管理功能
  addPage: (name) => {
    const pageId = `page-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
    const newPage: AlbumPage = {
      id: pageId,
      name: name || `页面 ${get().pages.length + 1}`,
      thumbnail: "",
      canvasData: "",
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    set((state) => ({
      pages: [...state.pages, newPage],
      currentPageId: pageId,
    }))

    return pageId
  },

  deletePage: (pageId) => {
    set((state) => ({
      pages: state.pages.filter((page) => page.id !== pageId),
      currentPageId:
        state.currentPageId === pageId ? null : state.currentPageId,
    }))
  },

  updatePage: (pageId, updates) => {
    set((state) => ({
      pages: state.pages.map((page) =>
        page.id === pageId
          ? { ...page, ...updates, updatedAt: new Date() }
          : page,
      ),
    }))
  },

  reorderPages: (fromIndex, toIndex) => {
    set((state) => {
      const newPages = [...state.pages]
      const [movedPage] = newPages.splice(fromIndex, 1)
      newPages.splice(toIndex, 0, movedPage)
      return { pages: newPages }
    })
  },

  setCurrentPage: (pageId) => {
    set({ currentPageId: pageId })
  },

  savePage: (pageId, canvasData, thumbnail) => {
    set((state) => ({
      pages: state.pages.map((page) =>
        page.id === pageId
          ? {
              ...page,
              canvasData,
              thumbnail,
              updatedAt: new Date(),
            }
          : page,
      ),
    }))
  },

  loadPage: (pageId) => {
    const { pages } = get()
    return pages.find((page) => page.id === pageId) || null
  },

  generateThumbnail: async () => {
    const { editor } = get()
    if (!editor) return ""

    try {
      // 生成缩略图
      const dataURL = editor.export.toImage({
        pixelRatio: 0.3, // 降低分辨率以减小文件大小
        quality: 0.8,
      })
      return dataURL
    } catch (error) {
      console.error("生成缩略图失败:", error)
      return ""
    }
  },
}))
