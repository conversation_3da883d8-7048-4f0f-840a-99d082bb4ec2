import { TanStackLink } from "@/components/custom/link";
import { LocaleToggle } from "@/components/util/locale-toggle";
import { ThemeToggle } from "@/components/util/theme-toggle";
import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/_public/")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <>
      <div>Hello "/_public/"!</div>
      <TanStackLink to="/" variant="link">
        Index
      </TanStackLink>
      <hr />
      <TanStackLink to="/about" variant="link">
        About
      </TanStackLink>
      <hr />
      <TanStackLink to="/pokemon" variant="link">
        Pokemon
      </TanStackLink>
      <hr />
      <TanStackLink to="/home" variant="link">
        Home
      </TanStackLink>
      <hr />
      <TanStackLink to="/sign-in" variant="link">
        Sign In
      </TanStackLink>
      <TanStackLink to="/canva" variant="link">
        posterEditor
      </TanStackLink>
      <hr />
      <LocaleToggle />
      <hr />
      <ThemeToggle />
      <hr />
    </>
  );
}
