import { Page } from "@/components/Page";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useState } from "react";
export const Route = createFileRoute("/_public/preview")({
	component: RouteComponent,
});

const width = 2480;
const height = 2000;
const viewportWidth = document.documentElement.clientWidth;

const scale = Number((viewportWidth / width).toFixed(4));
console.log("🚀 ~ scale:", scale);

function RouteComponent() {
	const canvasData = [
		{
			stage: {
				attrs: {
					width,
					height,
					x: 0,
					y: 0,
				},
				filters: [],
				className: "Stage",
			},
			layer: {
				attrs: { x: 0, y: 0, width, height },
				filters: [],
				className: "Layer",
			},
			background: {
				image: {
					attrs: { x: 0, y: 0 },
					filters: [],
					className: "Image",
					zIndex: 0,
				},
				overlay: {
					attrs: { x: 0, y: 0 },
					filters: [],
					className: "Rect",
					zIndex: 1,
				},
			},
			shapes: [
				{
					attrs: {
						fill: "red",
						radius: 50,
						x: 513,
						y: 143,
						width: 100,
						height: 100,
					},
					filters: [],
					className: "Circle",
					zIndex: 2,
				},
				{
					attrs: { x: 600, y: 300, width: 407.81, height: 40 },
					filters: [],
					className: "Label",
					zIndex: 3,
					children: [
						{
							attrs: { fill: "transparent", width: 407.8125, height: 40 },
							className: "Tag",
						},
						{
							attrs: {
								text: "Pikaso is great, isn't it?",
								fontSize: 40,
								fontWeight: "bold",
								fill: "purple",
								height: "auto",
							},
							className: "Text",
						},
					],
				},
				{
					attrs: {
						width: 250,
						height: 250,
						x: 197,
						y: 146,
						url: "https://edu-media.ancda.com/prod/archives/theme/cover5.png",
					},
					filters: [],
					className: "Image",
					zIndex: 4,
				},
			],
		},
		{
			stage: {
				attrs: {
					width,
					height,
					x: 0,
					y: 0,
				},
				filters: [],
				className: "Stage",
			},
			layer: {
				attrs: { x: 0, y: 0, width, height },
				filters: [],
				className: "Layer",
			},
			background: {
				image: {
					attrs: { x: 0, y: 0 },
					filters: [],
					className: "Image",
					zIndex: 0,
				},
				overlay: {
					attrs: { x: 0, y: 0 },
					filters: [],
					className: "Rect",
					zIndex: 1,
				},
			},
			shapes: [
				{
					attrs: {
						fill: "red",
						radius: 50,
						x: 513,
						y: 143,
						width: 100,
						height: 100,
					},
					filters: [],
					className: "Circle",
					zIndex: 2,
				},
				{
					attrs: { x: 600, y: 300, width: 407.81, height: 40 },
					filters: [],
					className: "Label",
					zIndex: 3,
					children: [
						{
							attrs: { fill: "transparent", width: 407.8125, height: 40 },
							className: "Tag",
						},
						{
							attrs: {
								text: "Pikaso is great, isn't it?4444",
								fontSize: 40,
								fontWeight: "bold",
								fill: "purple",
								height: "auto",
							},
							className: "Text",
						},
					],
				},
				{
					attrs: {
						width: 250,
						height: 250,
						x: 197,
						y: 146,
						url: "https://edu-media.ancda.com/prod/archives/theme/cover5.png",
					},
					filters: [],
					className: "Image",
					zIndex: 4,
				},
			],
		},
	];

	const [data, setData] = useState(canvasData);

	// useEffect(() => {
	// 	setData(
	// 		canvasData.map((item) => ({
	// 			...item,
	// 			stage: {
	// 				...item.stage,
	// 				attrs: { ...item.stage.attrs, scaleX: scale, scaleY: scale },
	// 			},
	// 		})),
	// 	);
	// }, [scale]);

	return (
		<div className="">
			{data.length &&
				data.map((item) => (
					<Page value={JSON.stringify(item)} key={Math.random()} />
				))}
		</div>
	);
}
