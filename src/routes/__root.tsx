import { Outlet, createRootRouteWithContext } from "@tanstack/react-router";
import { createTranslator } from "use-intl";

import { Button } from "@/components/base/button";

import { TanStackQueryDevTools } from "@/components/devtools/tanstack-query";
import { TanStackRouterDevTools } from "@/components/devtools/tanstack-router";

import { getI18NQueryOptions } from "@/config/i18n";
import type { RouterContext } from "@/config/tanstack-router";
import { I18NProvider } from "@/providers/intl-provider";
import {
  getCurrentUserStatus,
  userQueries,
} from "@/services/queries/user.query";
import { useState } from "react";

export const Route = createRootRouteWithContext<RouterContext>()({
  beforeLoad: async ({ context: { queryClient, currentLocale } }) => {
    const messagesPromise = queryClient.ensureQueryData(
      getI18NQueryOptions(currentLocale),
    );
    const currentUserStatusPromise = getCurrentUserStatus(
      queryClient.ensureQueryData(userQueries.meQueryOptions(false)),
    );

    const [messages, currentUserStatus] = await Promise.all([
      messagesPromise,
      currentUserStatusPromise,
    ]);

    const translator = createTranslator({
      locale: currentLocale,
      messages,
    });

    return {
      translator,
      currentUserStatus,
    };
  },
  component: RootComponent,
});

function RootComponent() {
  const [showDevtools, setShowDevtools] = useState<boolean>(false);

  return (
    <I18NProvider>
      <Outlet />
      <Button
        size="sm"
        onClick={() => {
          setShowDevtools((prevState) => !prevState);
        }}
        className="absolute top-20 left-0"
      >
        Devtools
      </Button>
      <TanStackRouterDevTools showDevtools={showDevtools} />
      <TanStackQueryDevTools showDevtools={showDevtools} />
    </I18NProvider>
  );
}
